from django.db import models
from django.utils.translation import gettext_lazy as _


class VerificationStatusChoices(models.TextChoices):
    PENDING = "PND", _("Pending")
    VERIFIED = "VER", _("Verified")
    REJECTED = "REJ", _("Rejected")


class ACCOUNT_TYPE_CHOICES(models.TextChoices):
    PERSONAL = "personal", _("Personal")
    BUSINESS = "business", _("Business/Organization")
    GOVERNMENT = "government", _("Government")
    OFFICIAL = "official", _("Government Official")


class CATEGORY_CHOICES(models.TextChoices):
    PERSONAL = (
        "personal",
        _("Personal"),
    )
    POLITICS = (
        "politics",
        _("Politics"),
    )
    ENTERTAINMENT = (
        "entertainment",
        _("Entertainment"),
    )
    GOVERNMENT = (
        "government",
        _("Government"),
    )
    SPORTS = (
        "sports",
        _("Sports"),
    )
    NEWS = (
        "news",
        _("News/Media"),
    )
    OTHER = "other", _("Other")


class PRIVACY_CHOICES(models.TextChoices):
    EVERYONE = (
        "everyone",
        _("Everyone"),
    )
    NO_ONE = (
        "no_one",
        _("Nobody"),
    )
    FOLLOWING = (
        "following",
        _("Following"),
    )
    FOLLOWERS_AND_FOLLOWING = "followers_and_following", _("Followers and Following")
