from rest_framework import serializers
from .models import Hub
from core.serializers import UserSerializer
from core.models import User


class HubSerializer(serializers.ModelSerializer):
    """Serializer for Hub model."""

    user = UserSerializer(read_only=True)
    contributors = UserSerializer(many=True, read_only=True)
    contributor_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        write_only=True,
        source="contributors",
        queryset=User.objects.all(),
        required=False,
    )

    class Meta:
        model = Hub
        fields = ["id", "user", "name", "contributors", "contributor_ids"]

    def create(self, validated_data):
        contributors = validated_data.pop("contributors", [])
        # Get the user from the context
        user = self.context["request"].user
        # Create hub with the user
        hub = Hub.objects.create(user=user, **validated_data)
        if contributors:
            hub.contributors.set(contributors)
        return hub
