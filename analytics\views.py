
from django.utils import timezone
from datetime import timedelta
from dateutil.parser import parse as parse_date
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import models
from django.db.models import Count, Sum
from django.shortcuts import get_object_or_404
from rest_framework.permissions import IsAuthenticated
from .models import FollowerCount, AccountsPerformance, ProfileViewTrack, MentionCount
from post.models import Post, Boost
from .serializers import (
    FollowerCountSerializer,
    MentionCountSerializer,
    AccountsPerformanceSerializer,
    ProfileViewsTrackSerializer,
)
from post.serializers import PostSerializer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from datetime import datetime
from django.db.models.functions import TruncDate
from django.contrib.contenttypes.models import ContentType



def get_date_range(request):
    """Utility to extract date range from request query parameters."""
    option = request.query_params.get("date_range_option")
    now = timezone.now()

    if option == "last_7_days":
        return now - timedelta(days=7), now
    elif option == "last_30_days":
        return now - timedelta(days=30), now
    elif option == "last_60_days":
        return now - timedelta(days=60), now
    elif option == "custom_range":
        start = request.query_params.get("start_date")
        end = request.query_params.get("end_date")
        if start and end:
            try:
                start_date = datetime.strptime(start, "%Y-%m-%d")
                end_date = datetime.strptime(end, "%Y-%m-%d")
                return start_date, end_date
            except ValueError:
                raise ValueError("Invalid date format. Use YYYY-MM-DD.")
        raise ValueError("Both start_date and end_date are required for custom range.")

    raise ValueError("Invalid or missing date_range_option.")


class FollowerCountView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get total follower count and daily follower counts over a time range.",
        manual_parameters=[
            openapi.Parameter(
                "date_range_option",
                openapi.IN_QUERY,
                description="Choose from: last_7_days, last_30_days, last_60_days, custom_range",
                type=openapi.TYPE_STRING,
                enum=["last_7_days", "last_30_days", "last_60_days", "custom_range"],
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Start date for custom range (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="End date for custom range (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Success response with total count and daily follower breakdown.",
                examples={
                    "application/json": {
                        "total_count": 23,
                        "followers": [
                            {"date": "2025-04-25", "count": 4},
                            {"date": "2025-04-26", "count": 5},
                            {"date": "2025-04-27", "count": 14},
                        ],
                    }
                },
            ),
            400: openapi.Response(
                description="Bad request if date format is invalid.",
                examples={
                    "application/json": {
                        "error": "Invalid date format. Please use YYYY-MM-DD."
                    }
                },
            ),
        },
    )
    def get(self, request):
        try:
            start_date, end_date = get_date_range(request)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        daily_data = (
            FollowerCount.objects.filter(
                user=request.user, timestamp__date__range=(start_date, end_date)
            )
            .annotate(date=TruncDate("timestamp"))
            .values("date")
            .annotate(count=Count("id"))
            .order_by("date")
        )

        followers = [
            {"date": entry["date"], "count": entry["count"]} for entry in daily_data
        ]
        total_count = sum(entry["count"] for entry in daily_data)

        return Response(
            {"total_count": total_count, "followers": followers},
            status=status.HTTP_200_OK,
        )


class MentionCountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        mentions = MentionCount.objects.filter(user=request.user)
        serializer = MentionCountSerializer(mentions, many=True)
        return Response(serializer.data)


class AccountPerformanceView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get performance data for account analytics in chart-friendly format.",
        manual_parameters=[
            openapi.Parameter(
                "date_range_option",
                openapi.IN_QUERY,
                description="Date range filter (e.g., 'last_7_days', 'last_30_days', 'last_60_days', 'custom_range')",
                type=openapi.TYPE_STRING,
                enum=["last_7_days", "last_30_days", "last_60_days", "custom_range"],
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Custom start date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="Custom end date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "account_type",
                openapi.IN_QUERY,
                description="Optional filter by account type",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Analytics performance data",
                examples={
                    "application/json": {
                        "followers": {
                            "total": 23,
                            "count": 3,
                            "date_range": [
                                {"date": "2025-04-25", "count": 4},
                                {"date": "2025-04-26", "count": 5},
                                {"date": "2025-04-27", "count": 14},
                            ],
                        },
                        "profile_visits": {
                            "total": 11,
                            "count": 3,
                            "date_range": [
                                {"date": "2025-04-25", "count": 3},
                                {"date": "2025-04-26", "count": 4},
                                {"date": "2025-04-27", "count": 4},
                            ],
                        },
                        "performance_summary": [
                            {
                                "title": "Posts",
                                "total": 78,
                                "percentage": 25,
                                "trend": "up",
                            },
                            {
                                "title": "Followers",
                                "total": 45,
                                "percentage": -10,
                                "trend": "down",
                            },
                            {
                                "title": "Post Boosts",
                                "total": 12,
                                "percentage": 40,
                                "trend": "up",
                            },
                            {
                                "title": "Mentions",
                                "total": 0,
                                "percentage": 0,
                                "trend": "neutral",
                            },
                        ],
                        "top_posts": {
                            "most_liked": {"id": 101, "likes": 250},
                            "most_commented": {"id": 95, "comments": 80},
                            "most_boosted": {"id": 89, "boosts": 14},
                        },
                    }
                },
            ),
            400: openapi.Response(description="Invalid date format or missing range"),
            404: openapi.Response(description="No data found"),
        },
    )
    def get(self, request):
        user = request.user

        # Get the start_date and end_date
        try:
            start_date, end_date = get_date_range(request)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # Followers chart data
        followers_qs = FollowerCount.objects.filter(
            user=user, timestamp__range=(start_date, end_date)
        ).order_by("timestamp")

        followers_data = [
            {"date": entry.timestamp.date(), "count": entry.count}
            for entry in followers_qs
        ]

        # Profile visits chart data
        visits_qs = ProfileViewTrack.objects.filter(
            user=user, timestamp__range=(start_date, end_date)
        ).order_by("timestamp")

        visits_data = [
            {"date": entry.timestamp.date(), "count": 1} for entry in visits_qs
        ]

        # NEW: Get posts within date range
        posts_qs = Post.objects.filter(
            user=user, post_date__range=(start_date, end_date)
        )

        # Calculate like count from related 'likes'
        like_count = (
            posts_qs.annotate(like_total=Count("likes")).aggregate(
                total=Sum("like_total")
            )["total"]
            or 0
        )     

        # Calculate boost count using Boost model
    
        post_type = ContentType.objects.get_for_model(Post)
        boost_count = Boost.objects.filter(
            content_type=post_type,
            object_id__in=posts_qs.values_list('id', flat=True)
        ).count()
        
        # Function for building performance summary

        def build_summary(title, current_total):
            delta = end_date - start_date
            prev_start = start_date - delta
            prev_end = start_date

            # Previous posts within previous date range
            prev_posts_qs = Post.objects.filter(
                user=user, post_date__range=(prev_start, prev_end)
            )

            if title == "Posts Likes":
                prev_total = (
                    prev_posts_qs.annotate(like_total=Count("likes")).aggregate(
                        total=Sum("like_total")
                    )["total"]
                    or 0
                )
            elif title == "Post Boosts":
                prev_post_ids = prev_posts_qs.values_list('id', flat=True)
                prev_total = Boost.objects.filter(
                    content_type=post_type,
                    object_id__in=prev_post_ids
                ).count()
            elif title == "Followers":
                prev_total = (
                    FollowerCount.objects.filter(
                        user=user, timestamp__range=(prev_start, prev_end)
                    ).aggregate(total=Sum("count"))["total"]
                    or 0
                )
            elif title == "Mentions":
                prev_total = (
                    MentionCount.objects.filter(
                        user=user, timestamp__range=(prev_start, prev_end)
                    ).aggregate(total=Sum("count"))["total"]
                    or 0
                )
            else:
                prev_total = 0

            # Adjusted LinkedIn-style percentage logic
            if prev_total == 0:
                if current_total == 0:
                    percentage = 0
                else:
                    baseline = 5  # can be tuned depending on expected volume
                    percentage = min((current_total / baseline) * 100, 100)
            else:
                percentage = ((current_total - prev_total) / prev_total) * 100

            if current_total > prev_total:
                trend = "up"
            elif current_total < prev_total:
                trend = "down"
            else:
                trend = "neutral"

            return {
                "title": title,
                "total": current_total,
                "percentage": round(percentage),
                "trend": trend,
            }

        # Followers total
        follower_count = sum([entry["count"] for entry in followers_data])

        # Mentions
        total_mentions = (
            MentionCount.objects.filter(
                user=user, timestamp__range=(start_date, end_date)
            ).aggregate(total=Sum("count"))["total"]
            or 0
        )

        # Build performance summary
        performance_summary = [
            build_summary("Posts Likes", like_count),
            build_summary("Followers", follower_count),
            build_summary("Post Boosts", boost_count),
            build_summary("Mentions", total_mentions),
        ]

        # Top posts logic
        top_posts = {}

        most_liked = (
            posts_qs.annotate(like_total=Count("likes"))
            .order_by("-like_total")
            .values("id", "like_total")
            .first()
        )
        if most_liked:
            top_posts["most_liked"] = {
                "id": most_liked["id"],
                "likes": most_liked["like_total"],
            }

        most_commented = (
            posts_qs.annotate(comment_total=Count("comments"))
            .order_by("-comment_total")
            .values("id", "comment_total")
            .first()
        )
        if most_commented:
            top_posts["most_commented"] = {
                "id": most_commented["id"],
                "comments": most_commented["comment_total"],
            }

        most_boosted = (
            posts_qs.annotate(boost_total=Boost.objects.filter(
                content_type=post_type,
                object_id=models.OuterRef('id')
            ).values('id').annotate(count=models.Count('id')).values('count'))
            .order_by('-boost_total')
            .values('id', 'boost_total')
            .first()
        )
        if most_boosted:
            top_posts["most_boosted"] = {
                "id": most_boosted["id"],
                "boosts": most_boosted["boost_total"],
            }

        return Response(
            {
                "followers": {
                    "total": follower_count,
                    "count": len(followers_data),
                    "date_range": followers_data,
                },
                "profile_visits": {
                    "total": len(visits_data),
                    "count": len(visits_data),
                    "date_range": visits_data,
                },
                "performance_summary": performance_summary,
                "top_posts": top_posts,
            },
            status=status.HTTP_200_OK,
        )


class ProfileViewTrackView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                "date_range_option",
                openapi.IN_QUERY,
                description="Choose from 'last_7_days', 'last_30_days', 'last_60_days', or 'custom_range'",
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Required if 'custom_range' is selected. Format: YYYY-MM-DD",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="Required if 'custom_range' is selected. Format: YYYY-MM-DD",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ]
    )
    def get(self, request):
        try:
            start_date, end_date = get_date_range(request)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        profile_views = ProfileViewTrack.objects.filter(
            user=request.user, timestamp__range=(start_date, end_date)
        )
        serializer = ProfileViewsTrackSerializer(profile_views, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class PostPerformances(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Post Performance Stats",
        manual_parameters=[
            openapi.Parameter(
                "date_range_option",
                openapi.IN_QUERY,
                description="Options: last_7_days, last_30_days, last_60_days, custom_range",
                type=openapi.TYPE_STRING,
                required=True,
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Start date for custom range (format: YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="End date for custom range (format: YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
            ),
        ],
        responses={200: "Post performance data with most likes, comments and boosts"},
    )
    def get(self, request):
        try:
            start_date, end_date = get_date_range(request)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        posts = Post.objects.filter(
            user=request.user, post_date__range=(start_date, end_date)
        )

        # Define post_type for Boost queries
        post_type = ContentType.objects.get_for_model(Post)

        post_with_max_likes = (
            posts.annotate(num_likes=Count("likes")).order_by("-num_likes").first()
        )
        post_with_max_comments = (
            posts.annotate(num_comments=Count("comments"))
            .order_by("-num_comments")
            .first()
        )
        post_with_max_boosts = None
        boost_counts = Boost.objects.filter(
            content_type=post_type,
            object_id__in=posts.values_list('id', flat=True)
        ).values('object_id').annotate(boost_count=models.Count('id')).order_by('-boost_count')
        if boost_counts:
            max_boost = boost_counts[0]
            post_with_max_boosts = posts.get(id=max_boost['object_id'])

        post_likes_serializer = (
            PostSerializer(post_with_max_likes) if post_with_max_likes else None
        )
        post_comments_serializer = (
            PostSerializer(post_with_max_comments) if post_with_max_comments else None
        )
        post_boosts_serializer = (
            PostSerializer(post_with_max_boosts) if post_with_max_boosts else None
        )

        return Response(
            {
                "max_likes_post": post_likes_serializer.data
                if post_likes_serializer
                else {},
                "max_comments_post": post_comments_serializer.data
                if post_comments_serializer
                else {},
                "max_boosts_post": post_boosts_serializer.data
                if post_boosts_serializer
                else {},
            },
            status=status.HTTP_200_OK,
        )
