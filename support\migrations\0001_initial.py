# Generated by Django 4.2.1 on 2024-01-24 08:32

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SupportTicket",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON>ield(max_length=200)),
                ("email", models.EmailField(max_length=254)),
                ("mail_Subject", models.Char<PERSON><PERSON>(max_length=100)),
                ("mail_content", models.TextField()),
            ],
        ),
    ]
