from django.test import TestCase
from .models import User, PrivacySettings
from django.contrib.auth import get_user_model
from django.urls import reverse
from notifications.models import Notification
from rest_framework.test import APIClient
from chat.models import Connection
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework import status


class UsersManagersTestCase(TestCase):
    def test_create_user(self):
        User = get_user_model()
        user = User.objects.create_user(
            email="<EMAIL>", password="testpassword"
        )
        self.assertEqual(user.email, "<EMAIL>")
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_superuser(self):
        User = get_user_model()
        admin_user = User.objects.create_superuser(
            email="<EMAIL>", password="foo"
        )
        self.assertEqual(admin_user.email, "<EMAIL>")
        self.assertTrue(admin_user.is_active)
        self.assertTrue(admin_user.is_staff)
        self.assertTrue(admin_user.is_superuser)


class UserTestCase(TestCase):
    def setUp(self):
        User = get_user_model()
        self.user_a = User.objects.create_user(
            email="<EMAIL>", password="testpassa", fcm_token=["token"]
        )
        self.user_b = User.objects.create_user(
            email="<EMAIL>", password="testpassb"
        )

    def get_client(self):
        client = APIClient()
        client.force_authenticate(user=self.user_a)
        return client

    def test_user_update_endpoint(self):
        client = self.get_client()
        url = reverse("core:User-detail", kwargs={"pk": self.user_a.pk})
        set_bio = "new bio yaay"
        response = client.patch(url, {"bio": set_bio}, format="json")
        response_data = response.json()
        self.assertEqual(response.status_code, 200)
        self.user_a.refresh_from_db()
        bio = self.user_a.bio
        self.assertEqual(bio, set_bio)

    def test_user_retrieve_endpoint(self):
        client = self.get_client()
        self.user_b.followers.add(self.user_a)
        url = reverse("core:User-detail", kwargs={"pk": self.user_b.pk})
        response = client.get(url, format="json")
        print(response.json())
        data = response.json()
        self.assertEqual(data["is_connected"], True)

    def new_function(so_nice):
        return "New Video Out Soon!!!"

    def test_verify_user_endpoint(self):
        client = self.get_client()
        token = RefreshToken.for_user(self.user_a).access_token
        # url = f"core:api/v1/user/verify_email/?token={str(token)}"
        url = reverse("core:User-verify-email") + f"?token={str(token)}"
        response = client.get(url, format="json")
        # print(RefreshToken.for_user(self.user_a).access_token)
        response_data = response.json()
        self.assertEqual(
            response_data["email"],
            "<NAME_EMAIL>'s account",
        )
        self.assertEqual(response.status_code, 200)
        # print(response.json())

    def test_login_view(self):
        client = self.get_client()
        response = client.post(
            reverse("core:token_obtain_pair"),
            {
                "email": "<EMAIL>",
                "password": "testpassa",
                "fcm_token": [
                    "token",
                ],
            },
        )

        self.assertEqual(response.status_code, 403)  # newly created user isnt verified
        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.fcm_token, "['token']")

    def test_login_without_token(self):
        client = self.get_client()
        # Activating the user
        token = RefreshToken.for_user(self.user_a).access_token
        # url = f"core:api/v1/user/verify_email/?token={str(token)}"
        activation_url = reverse("core:User-verify-email") + f"?token={str(token)}"
        activation_response = client.get(activation_url, format="json")
        # activation done
        response = client.post(
            reverse("core:token_obtain_pair"),
            {
                "email": "<EMAIL>",
                "password": "testpassa",
            },
        )
        self.assertEqual(response.status_code, 200)
        # print(response.json())
        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.fcm_token, None)

    def test_deactivate_user_endpoint(self):
        client = self.get_client()
        url = reverse("core:User-deactivate-account")
        response = client.patch(url, format="json")
        # to verify behaviour if the user has been deactivated before.
        response2 = client.patch(url, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response2.json()["status"], "Already Deactivated")

    def test_user_created(self):
        self.assertEqual(2, User.objects.all().count())

    def test_following(self):
        self.user_a.following.add(self.user_b)
        following_count = self.user_b.followers.all().count()
        self.assertEqual(1, following_count)

    def test_follow_api_endpoint(self):
        client = self.get_client()
        response = client.post(
            reverse("core:user-follow", kwargs={"username": self.user_b.username}),
            {"action": "follow"},
            format="json",
        )
        response_data = response.json()
        count = response_data.get("Followers Count")
        connection = Connection.objects.prefetch_related("sender", "receiver")
        self.assertEqual(count, 1)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(connection.count(), 1)
        self.assertEqual(connection.first().accepted, True)

    def test_unfollow_api_endpoint(self):
        client = self.get_client()
        response = client.post(
            reverse("core:user-follow", kwargs={"username": self.user_b.username}),
            {"action": "unfollow"},
            format="json",
        )
        response_data = response.json()
        count = response_data.get("Followers Count")
        connection = Connection.objects.prefetch_related("sender", "receiver")
        self.assertEqual(count, 0)
        self.assertEqual(connection.count(), 0)
        self.assertEqual(response.status_code, 200)

    def test_same_user_follow_api_endpoint(self):
        client = self.get_client()
        response = client.post(
            reverse("core:user-follow", kwargs={"username": self.user_a.username}),
            {"action": "follow"},
            format="json",
        )
        response_data = response.json()
        count = response_data.get("Followers Count")
        self.assertEqual(count, 0)
        self.assertEqual(response.status_code, 200)


class NotificationTestCase(TestCase):
    def setUp(self) -> None:
        self.user_a = User.objects.create_user(
            email="<EMAIL>", password="testpassa", fcm_token=["token"]
        )

        self.user_b = User.objects.create_user(
            email="<EMAIL>", password="testpassb"
        )

    def get_client(self):
        client = APIClient()
        client.force_authenticate(self.user_a)
        return client

    def test_follow_notification_endpoint(self):
        client = self.get_client()
        response = client.post(
            reverse("core:user-follow", kwargs={"username": self.user_b.username}),
            {"action": "follow"},
            format="json",
        )
        notification_objects = Notification.objects.all().count()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(notification_objects, 1)


class VerificationRequestTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.url = reverse("core:request-verification")

        # Base data required for all account types
        self.valid_data = {
            "account_type": "personal",
            "account_username": "testuser",
            "email": "<EMAIL>",
            "legal_name": "Test User",
            "date_of_birth": "1990-01-01",
            "government_id": "https://example.com/id.jpg",
            "proof_of_address": "https://example.com/address.jpg",
            "recent_photo": "https://example.com/photo.jpg",
            "reason_for_verification": "Testing verification process",
            "additional_info": "Additional test information",
        }

    def test_successful_verification_request(self):
        response = self.client.post(self.url, self.valid_data, format="json")
        print(f"Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["account_type"], "personal")
        self.assertEqual(response.data["account_username"], "testuser")
        self.assertEqual(response.data["email"], "<EMAIL>")

    def test_prevent_multiple_verification_requests(self):
        # Create first verification request
        self.client.post(self.url, self.valid_data, format="json")

        # Try to create second verification request
        response = self.client.post(self.url, self.valid_data, format="json")
        print(f"Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_authentication_required(self):
        client = APIClient()  # Unauthenticated client
        response = client.post(self.url, self.valid_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_validation_required_fields(self):
        # Test missing required fields
        for field in ["account_type", "account_username", "email"]:
            data = self.valid_data.copy()
            del data[field]
            response = self.client.post(self.url, data, format="json")
            print(f"Missing {field} - Response data: {response.data}")  # Debug print
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn(field, response.data)

    def test_invalid_account_type(self):
        data = self.valid_data.copy()
        data["account_type"] = "invalid_type"
        response = self.client.post(self.url, data, format="json")
        print(f"Invalid account type - Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("account_type", response.data)

    def test_business_account_fields(self):
        data = self.valid_data.copy()
        data["account_type"] = "business"
        data["business_name"] = "Test Business"
        data["business_doc"] = "https://example.com/business_doc.jpg"
        data["business_address_proof"] = "https://example.com/business_address.jpg"
        data["business_premises_photo"] = "https://example.com/premises.jpg"

        response = self.client.post(self.url, data, format="json")
        print(f"Business account - Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["account_type"], "business")
        self.assertEqual(response.data["business_name"], "Test Business")

    def test_government_account_fields(self):
        data = self.valid_data.copy()
        data["account_type"] = "government"
        data["govt_entity_name"] = "Test Government Entity"
        data["govt_authorized_id"] = "https://example.com/govt_id.jpg"
        data[
            "govt_account_verification_letter"
        ] = "https://example.com/verification.jpg"
        data["govt_registration_docs"] = "https://example.com/registration.jpg"

        response = self.client.post(self.url, data, format="json")
        print(f"Government account - Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["account_type"], "government")
        self.assertEqual(response.data["govt_entity_name"], "Test Government Entity")

    def test_official_account_fields(self):
        data = self.valid_data.copy()
        data["account_type"] = "official"
        data["govt_agency"] = "Test Agency"
        data["official_id_card"] = "https://example.com/official_id.jpg"
        data["official_letter"] = "https://example.com/official_letter.jpg"

        response = self.client.post(self.url, data, format="json")
        print(f"Official account - Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["account_type"], "official")
        self.assertEqual(response.data["govt_agency"], "Test Agency")

    def test_invalid_email_format(self):
        data = self.valid_data.copy()
        data["email"] = "invalid-email"
        response = self.client.post(self.url, data, format="json")
        print(f"Invalid email - Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("email", response.data)

    def test_optional_fields(self):
        # Test with minimal required fields
        minimal_data = {
            "account_type": "personal",
            "account_username": "testuser",
            "email": "<EMAIL>",
        }
        response = self.client.post(self.url, minimal_data, format="json")
        print(f"Minimal data - Response data: {response.data}")  # Debug print
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["account_type"], "personal")
        self.assertEqual(response.data["account_username"], "testuser")
        self.assertEqual(response.data["email"], "<EMAIL>")






class PrivacySettingsTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(email="<EMAIL>", password="testpass")
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        # Use get_or_create to avoid IntegrityError if PrivacySettings already exists
        PrivacySettings.objects.get_or_create(user=self.user)

    def test_retrieve_privacy_settings(self):
        url = reverse("core:privacysettings-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data[0]["user"], self.user.id)

    def test_update_privacy_settings(self):
        url = reverse("core:privacysettings-detail", kwargs={"pk": self.user.privacysettings.pk})
        data = {"comments": "followers", "tags": "followers"}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, 200)
        self.user.privacysettings.refresh_from_db()
        self.assertEqual(self.user.privacysettings.comments, "followers")
        self.assertEqual(self.user.privacysettings.tags, "followers")

    def test_privacy_settings_requires_auth(self):
        client = APIClient()
        url = reverse("core:privacysettings-list")
        response = client.get(url)
        self.assertEqual(response.status_code, 401)

    def test_blockedlist_endpoint(self):
        # Add a blocked user
        blocked = User.objects.create_user(email="<EMAIL>", password="testpass")
        self.user.blocked_users.add(blocked)
        url = reverse("core:privacysettings-blockedlist")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["blocked_users"][0]["id"], blocked.id)

    def test_mutedlist_endpoint(self):
        # Add a muted user
        muted = User.objects.create_user(email="<EMAIL>", password="testpass")
        self.user.muted_users.add(muted)
        url = reverse("core:privacysettings-mutedlist")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["muted_users"][0]["id"], muted.id)