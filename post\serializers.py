from rest_framework import serializers
from core.models import User
from core.serializers import UserSerializer
from django.conf import settings
from .models import (
    Post,
    Comment,
    PostImage,
    Collection,
    SavedPost,
    ReportPost,
    Mention,
    Boost
)
from drf_writable_nested.serializers import WritableNestedModelSerializer
from django.contrib.contenttypes.models import ContentType

from django.utils import timezone
from datetime import datetime


TWEET_ACTION_OPTIONS = settings.TWEET_ACTION_OPTIONS

class BoostSerializer(serializers.ModelSerializer):
    content_type = serializers.CharField(write_only=True)
    object_id = serializers.IntegerField()
    target_type = serializers.SerializerMethodField()
    target = serializers.SerializerMethodField()

    class Meta:
        model = Boost
        fields = ['id', 'user', 'content_type', 'object_id', 'created_at', 'target_type', 'target']
        read_only_fields = ['id', 'user', 'created_at', 'target_type', 'target']

    def validate(self, data):
        model_name = data['content_type'].lower()
        allowed_models = ['post', 'comment']
        if model_name not in allowed_models:
            raise serializers.ValidationError("Boosting this content type is not allowed.")

        content_type = ContentType.objects.get(model=model_name)
        model_class = content_type.model_class()
        if not model_class.objects.filter(id=data['object_id']).exists():
            raise serializers.ValidationError("The target object does not exist.")
        
        data['content_type'] = content_type
        return data

    def create(self, validated_data):
        return Boost.objects.get_or_create(
            user=self.context['request'].user,
            content_type=validated_data['content_type'],
            object_id=validated_data['object_id']
        )[0]

    def get_target_type(self, obj):
        return obj.content_type.model  # e.g. "post" or "comment"

    def get_target(self, obj):
        if not hasattr(obj, '_cached_target'):
            obj._cached_target = obj.content_object

        target = obj._cached_target

        if obj.content_type.model == 'post':
            from .serializers import PostSerializer
            return PostSerializer(target).data
        elif obj.content_type.model == 'comment':
            from .serializers import CommentSerializer
            return CommentSerializer(target).data
        return None
    
class UnboostSerializer(serializers.Serializer):
    content_type = serializers.CharField()
    object_id = serializers.IntegerField()

    def validate(self, data):
        model_name = data['content_type'].lower()
        allowed_models = ['post', 'comment']
        if model_name not in allowed_models:
            raise serializers.ValidationError("Unboosting this content type is not allowed.")
        
        try:
            content_type = ContentType.objects.get(model=model_name)
        except ContentType.DoesNotExist:
            raise serializers.ValidationError("Invalid content type.")
        
        data['content_type'] = content_type
        return data


class PostMediaSerializer(serializers.ModelSerializer):
    media = serializers.SerializerMethodField()

    def get_media(self, obj):
        if isinstance(obj, str):
            return obj.replace("http", "https")
        elif obj.media:
            return obj.media.url.replace("http", "https")
        else:
            # print(obj,'-----------------')
            return None

    class Meta:
        model = PostImage
        fields = ["id", "media"]


class PostCreateSerializer(serializers.ModelSerializer):
    media = PostMediaSerializer(many=True, required=False)
    scheduled_date = serializers.DateField(
        required=False, help_text="Format: YYYY-MM-DD. Optional.", allow_null=True
    )
    scheduled_time = serializers.TimeField(
        required=False, help_text="Format: HH:MM:SS. Optional.", allow_null=True
    )

    class Meta:
        model = Post
        fields = (
            "id",
            "media",
            "parent",
            "content",
            "scheduled_date",
            "scheduled_time",
        )

    def validate(self, attrs):
        scheduled_date = attrs.get("scheduled_date")
        scheduled_time = attrs.get("scheduled_time")

        # Validation: If scheduled date is provided, the time must also be provided and vice versa.
        if scheduled_date and not scheduled_time:
            raise serializers.ValidationError(
                "Scheduled time is required when scheduling a post."
            )

        if scheduled_time and not scheduled_date:
            raise serializers.ValidationError(
                "Scheduled date is required when scheduling a post."
            )

        return attrs

    def create(self, validated_data):
        scheduled_date = validated_data.get("scheduled_date")
        scheduled_time = validated_data.get("scheduled_time")

        # Initialize scheduled_datetime as None
        scheduled_datetime = None
        is_published = False

        # Combine scheduled date and time into a datetime object if both are provided
        if scheduled_date and scheduled_time:
            try:
                # Combine date and time into a naive datetime
                naive_datetime = datetime.combine(scheduled_date, scheduled_time)
                scheduled_datetime = timezone.make_aware(
                    naive_datetime
                )  # Make it timezone-aware

                # If scheduled datetime is in the past, post goes live immediately
                if scheduled_datetime <= timezone.now():
                    is_published = True
            except Exception as e:
                raise serializers.ValidationError(
                    f"Error combining date and time: {str(e)}"
                )
        else:
            # If no scheduled date/time is provided, set the post to be published immediately
            is_published = True

        post = super().create(validated_data)

        post.is_published = is_published
        post.save()  # Save the post with the updated scheduled_datetime and is_published

        return post


class PostSerializer(serializers.ModelSerializer):
    media = PostMediaSerializer(many=True, read_only=True, source="images")
    parent = serializers.SerializerMethodField(read_only=True)
    user = UserSerializer(read_only=True)
    post_thread = serializers.SerializerMethodField(read_only=True)

    is_boosted = serializers.SerializerMethodField()
    boosted_by = serializers.SerializerMethodField()
    is_boosted_by_current_user = serializers.SerializerMethodField()
    boosts = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()

    class Meta:
        model = Post
        fields = [
            "id", "user", "post_thread",
            "is_boosted", "boosted_by",
            "is_boosted_by_current_user", "boosts",
            "media", "content", "parent", "post_date", "likes",
            "items_count", "is_published", "scheduled_date", "scheduled_time",
        ]

    def get_post_thread(self, obj):
        depth = self.context.get("depth", 0)
        if depth >= 2:
            return []
        thread_queryset = obj.post_thread.all()[:10]
        new_context = {**self.context, "depth": depth + 1}
        return PostSerializer(thread_queryset, many=True, context=new_context).data

    def get_parent(self, obj):
        depth = self.context.get("depth", 0)
        if obj.parent and depth < 2:
            new_context = {**self.context, "depth": depth + 1}
            return PostSerializer(obj.parent, context=new_context).data
        return None

    def get_is_boosted(self, obj):
        return hasattr(obj, "_boosted_by_user")

    def get_boosted_by(self, obj):
        if hasattr(obj, "_boosted_by_user"):
            return obj._boosted_by_user.username
        return None

    def get_is_boosted_by_current_user(self, obj):
        request = self.context.get("request")
        if not request or not hasattr(obj, "boosts"):
            return False
        return obj.boosts.filter(user=request.user).exists()

    def get_boosts(self, obj):
        boosts = obj.boosts.select_related("user")  # thanks to GenericRelation
        return [
            {
                "id": boost.id,
                "user_id": boost.user.id,
                "user": UserSerializer(boost.user).data,
                "boost_date": boost.created_at,
            }
            for boost in boosts
        ]

    def get_items_count(self, obj):
        return {
            "boost_count": obj.boosts.count(),
            "like_count": obj.likes.count(),
            "comment_count": obj.comments.count(),
        }


class CommentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    # post = PostSerializer(read_only=True)
    # post_id = serializers.PrimaryKeyRelatedField(queryset = Comment.objects.all(), write_only=True, source='comments')
    class Meta:
        model = Comment
        fields = ["id", "user", "post", "content", "parent", "timestamp", "likes"]


class MentionSerializer(serializers.ModelSerializer):
    user_details = serializers.SerializerMethodField()  # Read-only user info
    mentioned_by = serializers.SerializerMethodField()
    indices = serializers.SerializerMethodField()

    class Meta:
        model = Mention
        fields = [
            "id",
            "user",
            "user_details",
            "mentioned_by",
            "post",
            "comment",
            "created_at",
            "indices",
            "is_read",
        ]
        extra_kwargs = {
            "post": {"required": False},
            "comment": {"required": False},
        }

    def get_user_details(self, obj):
        return {
            "id": obj.user.id,
            "username": obj.user.username,
            "first_name": obj.user.first_name,
            "last_name": obj.user.last_name,
        }

    def get_mentioned_by(self, obj):
        author = None
        if obj.post:
            author = obj.post.user
        elif obj.comment:
            author = obj.comment.user

        if author:
            return {
                "id": author.id,
                "username": author.username,
                "first_name": author.first_name,
                "last_name": author.last_name,
            }
        return None

    def get_indices(self, obj):
        return {"start": obj.start_index, "end": obj.end_index}

    def create(self, validated_data):
        instance = super().create(validated_data)

        content = None
        if instance.post:
            content = instance.post.content
        elif instance.comment:
            content = instance.comment.content

        if content:
            mention_text = f"@{instance.user.username}"
            start = content.find(mention_text)
            if start != -1:
                instance.start_index = start
                instance.end_index = start + len(mention_text) - 1
                instance.save(update_fields=["start_index", "end_index"])

        return instance


class PostActionSerializer(serializers.Serializer):
    """
    Requires Post Id and Action to be taken e.g Like, Unlike, Boost with/without content(comment)
    """

    post_id = serializers.IntegerField()
    action = serializers.CharField()
    content = serializers.CharField(allow_blank=True, required=False)

    def validate_action(self, value):
        value = value.lower().strip()
        if value not in TWEET_ACTION_OPTIONS:
            raise serializers.ValidationError("This is not a valid action for posts")
        return value


class CommentActionSerializer(serializers.Serializer):
    """
    Requires Post Id and Action to be taken e.g Like, Unlike, Boost with/without content(comment)
    """

    comment_id = serializers.IntegerField()
    action = serializers.CharField()
    content = serializers.CharField(allow_blank=True, required=False)

    def validate_action(self, value):
        value = value.lower().strip()
        if value not in TWEET_ACTION_OPTIONS:
            raise serializers.ValidationError("This is not a valid action for posts")
        return value


class CollectionSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = Collection
        fields = ["id", "name", "user"]


class SavedPostSerializer(serializers.ModelSerializer):
    collection = serializers.PrimaryKeyRelatedField(queryset=Collection.objects.all())
    post = serializers.PrimaryKeyRelatedField(queryset=Post.objects.all())

    class Meta:
        model = SavedPost
        fields = ["id", "collection", "post"]

    def validate(self, data):
        """
        Ensure that the same post is not added to the same collection twice.
        """
        if SavedPost.objects.filter(
            collection=data["collection"], post=data["post"]
        ).exists():
            raise serializers.ValidationError(
                "This post is already saved in the collection."
            )
        return data


class ReportPostSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReportPost
        fields = "__all__"
        read_only_fields = ["user"]

    def validate(self, data):
        """
        Validates that the report is being submitted by the authenticated user.
        Prevents users from submitting reports on behalf of other users.
        """
        request_user = self.context.get("request").user
        if not request_user.is_authenticated:
            raise serializers.ValidationError(
                "User must be authenticated to submit a report"
            )
        return data
