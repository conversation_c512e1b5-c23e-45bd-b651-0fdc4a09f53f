# Generated by Django 4.2.1 on 2024-02-14 12:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0009_alter_verification_additional_docs_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "enable_comment_push_notifications",
                    models.<PERSON>oleanField(default=True),
                ),
                (
                    "direct_messages_push_notifications",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True),
                ),
                ("likes_push_notifications", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("enable_boost_push_notifications", models.BooleanField(default=True)),
                ("tags_to_post_push_notifications", models.BooleanField(default=True)),
                ("connections_push_notifications", models.Bo<PERSON>an<PERSON>ield(default=True)),
                ("blogs_post_push_notifications", models.<PERSON><PERSON><PERSON><PERSON>ield(default=True)),
                (
                    "direct_messages_email_notifications",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True),
                ),
                (
                    "promotional_email_from_smallworld",
                    models.BooleanField(default=True),
                ),
                ("two_factor_authentication_email", models.BooleanField(default=True)),
                (
                    "two_factor_authentication_google_auth",
                    models.BooleanField(default=True),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
