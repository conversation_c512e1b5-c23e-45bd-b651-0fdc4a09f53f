# Generated by Django 4.2.1 on 2024-03-15 00:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("analytics", "0007_remove_profileviewtrack_count"),
    ]

    operations = [
        migrations.AddField(
            model_name="profileviewtrack",
            name="viewed_by",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="profile_viewed_by",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=False,
        ),
    ]
