# Generated by Django 4.2.1 on 2025-04-12 11:37

from django.db import migrations, models
import django_countries.fields


class Migration(migrations.Migration):
    dependencies = [
        ("ads", "0005_remove_advert_preferred_location_reach_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CountryOption",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    django_countries.fields.CountryField(max_length=2, unique=True),
                ),
            ],
        ),
        migrations.RemoveField(
            model_name="advert",
            name="preferred_location_reach",
        ),
        migrations.AddField(
            model_name="advert",
            name="preferred_location_reach",
            field=models.ManyToManyField(
                blank=True,
                help_text="Select one or more target countries",
                related_name="adverts",
                to="ads.countryoption",
            ),
        ),
    ]
