from django.conf import settings
from django.db import IntegrityError
from rest_framework import status, mixins, serializers
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .permissions import UserPermissions
from .serializers import (
    EmailVerificationSerializer,
    ResetPasswordSerializer,
    TokenObtainPairSerializer,
    InterestChoiceSerializer,
    InterestSerializer,
    MailSerializer,
    UserSerializer,
    VerificationSerializer,
    NotificationSettingsSerializers,
    PrivacySettingsSerializers,
    BlogSerializer,
    InappNotificationSerializer,
)
from post.serializers import PostSerializer, PostMediaSerializer
from chat.models import Connection
from .models import (
    User,
    Interest,
    InterestChoice,
    NotificationSettings,
    PrivacySettings,
    Blog,
)
import django_countries, jwt, pyotp
from django.contrib.auth.hashers import make_password
from post.models import Post
from rest_framework import generics, status, viewsets
from random import sample, randrange
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken

from datetime import datetime, timedelta
from django.db.models import Q
from django.shortcuts import get_object_or_404
from notifications.signals import notify
from notifications.models import Notification
from .FCMManager import sendpush

from .tasks import send_login_validation, send_reset_login, send_signup_verification

from drf_yasg import openapi
from analytics.models import ProfileViewTrack
from django_filters import rest_framework as filters
from analytics.models import FollowerCount

import requests
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from urllib.parse import urlparse
from urllib.request import urlopen
import os
from drf_yasg.utils import swagger_auto_schema


def generate_verification_code(user):
    """
    Generatiing Random Verification For Users
    """
    code = ""
    for num in range(1, 5):
        code += str(randrange(0, 9))
    user.verification_id = code
    user.verification_id_created = datetime.now()
    user.save()


class UserViewSet(viewsets.ModelViewSet):
    serializer_class = UserSerializer
    queryset = User.objects.all()
    permission_classes = [UserPermissions]
    token_param_config = openapi.Parameter(
        "token",
        in_=openapi.IN_QUERY,
        description="Users",
        type=openapi.TYPE_STRING,
    )

    def create(self, request):
        serializer = self.serializer_class(data=request.data)

        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        uri = pyotp.totp.TOTP(serializer.data["mfa_hash"]).provisioning_uri(
            serializer.data["email"], issuer_name="SmallClosedWorld"
        )
        qrcode_uri = (
            "https://www.google.com/chart?chs=200x200&chld=M|0&cht=qr&chl={}".format(
                uri
            )
        )
        tokens = RefreshToken.for_user(user).access_token
        # verification_link = f"https://{settings.WEBSITE_URL}/api/v1/user/verify_email/?token={str(tokens)}"
        generate_verification_code(user)
        send_mail = send_signup_verification.delay(user.verification_id, user.email)
        if send_mail.status == "PENDING":
            mail_status = "Mail Sending Pending"
        elif send_mail.status == "SUCCESS":
            mail_status = "Mail Sent Successfully"
        else:
            mail_status = "Failed"

        return Response(
            {
                "status": "user created successfully",
                "qr_code": qrcode_uri,
                "data": serializer.data,
                "mail_status": mail_status,
                "access_token": str(tokens),
            },
            status=status.HTTP_201_CREATED,
        )

    def retrieve(self, request, pk=None):
        user_queryset = User.objects.all()
        user = get_object_or_404(user_queryset, pk=pk)

        # Track Profile Visits
        if request.user.is_authenticated and request.user != user:
            ProfileViewTrack.objects.create(user=user, viewed_by=request.user)
            is_connected = (
                user.followers.filter(username=request.user.username).exists()
                or request.user.followers.filter(username=user.username).exists()
            )
        else:
            is_connected = False
        # Serialize the user data
        user_serializer = UserSerializer(user)

        # Serialize the user's posts including media
        post_queryset = user.post.prefetch_related("images", "likes", "comments").all()
        post_serializer = PostSerializer(post_queryset, many=True)

        # Serialize only the posts with media
        media_queryset = post_queryset.filter(images__isnull=False).distinct()
        media_serializer = PostSerializer(media_queryset, many=True)

        # Prepare the response
        response_data = {
            "user": user_serializer.data,
            "posts": post_serializer.data,
            "all_media": media_serializer.data,
            "is_connected": is_connected,
        }

        return Response(response_data, status=status.HTTP_200_OK)
        # user_queryset = User.objects.all()
        # user = get_object_or_404(user_queryset, pk=pk)
        # is_connected = False
        # # Track Profile Visits
        # if request.user != user and request.user.is_authenticated:
        #     ProfileViewTrack.objects.create(user=user, viewed_by=request.user)
        #     is_connected = True if request.user.username in user.followers.values_list('username', flat=True) else False

        # user_serializer = UserSerializer(user)
        # post_queryset = user.post.all()
        # post_serializer = PostSerializer(post_queryset, many=True)
        # media_queryset = Post.objects.filter(user=user, images__isnull=False)
        # media_serializer = PostSerializer(media_queryset, many=True)
        # return Response(
        #     {
        #         "user": user_serializer.data,
        #         "posts": post_serializer.data,
        #         "all_media": media_serializer.data,
        #         "is_connected": is_connected
        #     },
        #     status=status.HTTP_200_OK,
        # )

    def partial_update(self, request, *args, **kwargs):
        pk = kwargs.get("pk")
        user = get_object_or_404(User, pk=pk) if pk else request.user

        data = request.data

        # Update basic fields
        user.first_name = data.get("first_name", user.first_name)
        user.last_name = data.get("last_name", user.last_name)
        user.username = data.get("username", user.username)
        user.bio = data.get("bio", user.bio)
        user.phone_number = data.get("phone_number", user.phone_number)
        user.birthday = data.get("birthday", user.birthday)
        if data.get("email"):
            user.email = data.get("email").lower()
        if data.get("country_of_residence"):
            user.country_of_residence = data.get("country_of_residence").lower()

        # Save uploaded or URL-based profile picture (field name and url key are the same)
        self.handle_file_upload_or_url(request, user, field_name="profile_picture")

        # Save uploaded or URL-based cover photo (field name and url key are the same)
        self.handle_file_upload_or_url(request, user, field_name="cover_photo")

        user.save()
        serializer = self.get_serializer(user)
        return Response(
            {
                "status": "success",
                "message": "User profile updated successfully.",
                "user": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    def handle_file_upload_or_url(self, request, user, field_name):
        # URL key is the same as field name
        url_key = field_name

        if field_name in request.FILES:
            setattr(user, field_name, request.FILES[field_name])
        elif url_key in request.data:
            url = request.data.get(url_key)
            if url:
                try:
                    response = requests.get(url)
                    response.raise_for_status()

                    filename = os.path.basename(urlparse(url).path)
                    file_content = ContentFile(response.content)

                    file_field = getattr(user, field_name, None)

                    if file_field and hasattr(file_field, "save"):
                        # This uses the model FileField's save method correctly
                        file_field.save(filename, file_content, save=False)
                    else:
                        uploaded_file = SimpleUploadedFile(filename, response.content)
                        setattr(user, field_name, uploaded_file)

                except requests.RequestException as e:
                    raise serializers.ValidationError(
                        {field_name: f"Could not download file: {str(e)}"}
                    )

    @action(detail=False, methods=["get"], permission_classes=[])
    def search(self, request):
        username = request.GET.get("username")
        users = User.objects.prefetch_related().filter(username__icontains=username)
        serialized_response = UserSerializer(users, many=True)
        return Response(
            {"usernames": serialized_response.data}, status=status.HTTP_200_OK
        )

    @action(detail=False, methods=["post"], permission_classes=[])
    def resend_otp(self, request):
        email_serializer = MailSerializer(data=request.data)
        email_serializer.is_valid(raise_exception=True)
        email = email_serializer.data["email"]
        user = get_object_or_404(User.objects.all(), email=email)
        uri = pyotp.totp.TOTP(user.mfa_hash).provisioning_uri(
            user.email, issuer_name="SmallClosedWorld"
        )
        qrcode_uri = (
            f"https://www.google.com/chart?chs=200x200&chld=M|0&cht=qr&chl={uri}"
        )
        access_token = RefreshToken.for_user(user).access_token
        refresh_token = RefreshToken.for_user(user)
        generate_verification_code(user)
        send_mail = send_signup_verification.delay(user.verification_id, user.email)
        if send_mail.status == "PENDING":
            mail_status = "Mail Sending Pending"
        elif send_mail.status == "SUCCESS":
            mail_status = "Mail Sent Successfully"
        else:
            mail_status = "Failed"

        return Response(
            {
                "status": "mail resent successfully",
                "qr_code": qrcode_uri,
                "mail_status": mail_status,
                "access_token": str(access_token),
                "refresh_token": str(refresh_token),
            },
            status=status.HTTP_201_CREATED,
        )

    @action(detail=False, methods=["get"], permission_classes=[])
    def verify_email(self, request):
        serializer_class = EmailVerificationSerializer
        self.permission_classes = []
        token = request.GET.get("token")
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            user = get_object_or_404(User.objects.all(), id=payload["user_id"])
            if not user.is_verified:
                user.is_verified = True
                user.save()
                return Response(
                    {"email": f"Successfully activated {user}'s account"},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"error": "User Already Verified"}, status=status.HTTP_418_IM_A_TEAPOT
            )
        except jwt.ExpiredSignatureError as identifier:
            return Response(
                {"error": "Activation Expired"}, status=status.HTTP_400_BAD_REQUEST
            )
        except jwt.exceptions.DecodeError as identifier:
            return Response(
                {"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=["get"], permission_classes=[])
    def return_countries(self, request):
        countries = []
        for code, country in django_countries.countries:
            countries.append({"code": code, "name": country})
        return Response(
            {"status": "Success", "code": 200, "data": [countries]}, status.HTTP_200_OK
        )

    @action(detail=False, methods=["get"], permission_classes=[])
    def verify_username(self, request):
        username = request.GET.get("username")
        if username in User.objects.values_list("username", flat=True):
            numbers = sample(range(0, 30), len([username] * 4))
            suggestions = [
                uname + str(num) for uname, num in zip([username] * 4, numbers)
            ]
            return Response(
                {
                    "status": "unavailable",
                    "code": 409,
                    "data": {"message": f"{username } is taken."},
                    "suggested_usernames": suggestions,
                },
                status.HTTP_409_CONFLICT,
            )
        else:
            return Response(
                {
                    "status": "available",
                    "code": 200,
                    "data": {"message": f"{username} is available."},
                },
                status.HTTP_200_OK,
            )

    @action(detail=False, methods=["get"], permission_classes=[])
    def email_check(self, request):
        email = request.GET.get("email")
        exists = User.objects.filter(email=email).exists()

        if exists:
            return Response(
                {
                    "status": "unavailable",
                    "code": 409,
                    "data": {"message": f"{email} already exists"},
                },
                status.HTTP_409_CONFLICT,
            )

        return Response(
            {
                "status": "available",
                "code": 200,
                "data": {"message": f"{email} is available."},
            },
            status.HTTP_200_OK,
        )

    @action(detail=False, methods=["patch"], permission_classes=[IsAuthenticated])
    def deactivate_account(self, request):
        user = request.user
        if user.is_active:
            user.is_active = False
            user.save()
            return Response(
                {
                    "status": "Successful",
                    "code": 200,
                    "data": {"message": f"{user.username} has been deactivated."},
                }
            )
        return Response(
            {
                "status": "Already Deactivated",
                "code": 200,
                "data": {"message": f"{user.username} is already deactivated."},
            }
        )


class PasswordResetMail(generics.GenericAPIView):
    serializer_class = MailSerializer
    Expiration_time = 30  # expires in 30 minutes

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = get_object_or_404(User, email=serializer.validated_data["email"])

        # Generate 4 Digit Code
        code = "".join(str(randrange(0, 9)) for _ in range(4))
        user.verification_id = code
        user.verification_id_created = datetime.now()
        user.save()

        # Send Mail
        send_mail = send_reset_login.delay(user.verification_id, user.email)

        if send_mail.status == "PENDING":
            mail_status = "Mail Sending Pending"
        elif send_mail.status == "SUCCESS":
            mail_status = "Mail Sent Successfully"
        else:
            mail_status = "Mail Sending Failed"

        return Response(
            {
                "status": "success",
                "data": f"verification code sent, expires in {self.Expiration_time} minutes after {user.verification_id_created} ",
                "code": 200,
                "mail_status": mail_status,
            },
            status=status.HTTP_200_OK,
        )


class VerifyCode(generics.GenericAPIView):
    serializer_class = EmailVerificationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        token = serializer.data["token"]
        user = get_object_or_404(User, verification_id=token)
        if user:
            expiration_time = user.verification_id_created + timedelta(
                minutes=PasswordResetMail.Expiration_time
            )
            now = datetime.now(expiration_time.tzinfo)

            # Check if the token is valid and not expired
            if token == user.verification_id and now <= expiration_time:
                user.is_verified = True
                user.save()
                return Response(
                    {
                        "status": "success",
                        "data": {
                            "name": user.username,
                        },
                        "code": 200,
                    },
                    status=status.HTTP_200_OK,
                )
        return Response(
            {
                "status": "Error",
                "data": "Invalid verification code.",
                "code": 401,
            },
            status=status.HTTP_401_UNAUTHORIZED,
        )


class ChangePassword(generics.GenericAPIView):
    serializer_class = ResetPasswordSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        code = serializer.data["code"]
        password = serializer.data["password"]
        confirm_password = serializer.data["confirm_password"]
        user = get_object_or_404(User, verification_id=code)
        if user:
            if password == confirm_password:
                password = make_password(request.data["password"])
                serializer.data["password"] = password
                user.password = password
                user.verification_id = ""
                user.save()

                return Response(
                    {
                        "status": "success",
                        "message": "Password has been successfully changed.",
                    },
                    status=200,
                )
            else:
                return Response(
                    {"status": "error", "message": "Passwords do not match."},
                    status=400,
                )
        else:
            return Response(
                {"status": "error", "message": "Invalid verification code."}, status=404
            )


class EmailTokenObtainPairView(TokenObtainPairView):
    """
    A subclass of TokenObtainPairView that obtains a pair of tokens for authentication and saves the Firebase Cloud Messaging (FCM) token for the authenticated user.

    Inherits the functionality of the TokenObtainPairView class and overrides the post method to save the FCM token for the authenticated user.

    Fields:
    - serializer_class: Specifies the serializer class to be used for obtaining the token pair. In this case, it is set to TokenObtainPairSerializer.
    """

    serializer_class = TokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        """
        Overrides the post method of the parent class to save the FCM token
        for the authenticated user.

        Args:
        - request: The HTTP request object.
        - *args: Additional positional arguments.
        - **kwargs: Additional keyword arguments.

        Returns:
        - The HTTP response object.
        """

        response = super().post(request, *args, **kwargs)
        data = request.data
        email = data.get("email", None)
        otp = data.get("otp", None)
        user = get_object_or_404(User, email=email)
        auth_settings_check = NotificationSettings.objects.get_or_create(user=user)
        auth_settings = auth_settings_check[0]
        if not user.is_verified:
            return Response(
                {"detail": "User is not verified."}, status=status.HTTP_403_FORBIDDEN
            )
        generate_verification_code(user)
        send_login_validation.delay(user.verification_id, To=user.email)

        if "fcm_token" in data:
            user.fcm_token = data.get("fcm_token", None)
            user.save()

        if auth_settings.two_factor_authentication_google_auth:
            totp = pyotp.TOTP(user.mfa_hash)
            # print(totp.now())
            if totp.verify(otp):
                # print("verified")
                pass
            else:
                return Response(
                    {"message": "invalid OTP"}, status=status.HTTP_401_UNAUTHORIZED
                )
        response.data["user"] = UserSerializer(user).data
        return response


class InterestChoiceView(viewsets.ModelViewSet):
    queryset = InterestChoice.objects.all()
    serializer_class = InterestChoiceSerializer


class InterestView(viewsets.ModelViewSet):
    queryset = Interest.objects.all()
    serializer_class = InterestSerializer


class BlogViewSet(viewsets.ModelViewSet):
    queryset = Blog.objects.all()
    serializer_class = BlogSerializer
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_fields = ["location", "interest"]


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def follow_view(request):
    user = request.user
    user_id = request.GET.get("user_id")
    if user_id:
        user = get_object_or_404(User, id=user_id)
    followers = user.followers.all()
    following = user.following.all()
    serialized_following = UserSerializer(following, many=True)
    serialized_followers = UserSerializer(followers, many=True)
    return Response(
        {
            "followers": serialized_followers.data,
            "following": serialized_following.data,
        },
        status=status.HTTP_200_OK,
    )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def action_view(request, username, *args, **kwargs):
    user = request.user
    subject_qs = get_object_or_404(User, username=username)
    if user.username == username:
        return Response(
            {
                "Followers Count": user.followers.all().count(),
                "followers": [x.username for x in user.followers.all()]
                or "No Followers",
                "status": 200,
            },
            status=200,
        )
    if not User.objects.filter(username=subject_qs).exists():
        return Response(
            {"error": "user not found", "status": 404},
            status=404,
        )
    subject = subject_qs
    data = request.data or {}
    action = data.get("action")
    action_done = None
    notification_status = {}
    notification_settings_get_and_check = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    notification_settings = notification_settings_get_and_check[0]

    if action:
        if action.lower() == "unfollow":
            subject.followers.remove(user)
            FollowerCount.objects.filter(user=subject, follower=user).delete()

            # Remove from Connections
            connection = Connection.objects.filter(Q(sender=user) & Q(receiver=subject))
            if connection:
                connection, _ = connection.delete()
            # TODO: Write Test for this, once it works test all others : Done
            action_done = "unfollowed"

        elif action.lower() == "follow":
            if user in subject.followers.all():
                return Response(
                    {
                        "Error": "User already followed.",
                    },
                    status=401,
                )
            else:
                subject.followers.add(user)
                FollowerCount.objects.create(user=subject, follower=user)

            # Save to connections model
            connection = Connection.objects.get_or_create(
                sender=user, receiver=subject, accepted=True
            )
            if notification_settings.connections_push_notifications:
                # Save to Notifications model
                notify.send(
                    sender=user,
                    recipient=subject,
                    verb="Followed You",
                    action_object=user,
                )
                # Send Firebase Notification
                send_notification = sendpush(
                    title="New Follower",
                    msg=f"{user} Followed You",
                    registration_token=user.fcm_token,
                    dataObject={"user": f"{user}"},
                )  # TODO: Write Test for this, once it works test all others : Done
                if send_notification:
                    notification_status[
                        f"{user} Follow"
                    ] = f"{[i.exception for i in send_notification.responses]}"
                else:
                    print(send_notification)
                action_done = "followed"

        elif action.lower() == "block":
            if subject in user.blocked_users.all():
                return Response(
                    {
                        "Error": "User already blocked.",
                    },
                    status=401,
                )
            user.blocked_users.add(subject)
            # Save to connections model

            connections = Connection.objects.filter(
                Q(
                    Q(sender=user) & Q(receiver=subject)
                    | Q(Q(sender=subject) & Q(receiver=user))
                )
            )
            if connections:
                for connection in connections:
                    connection.blocked = True
                    connection.save()
            action_done = "block"

        elif action.lower() == "unblock":
            if subject not in user.blocked_users.all():
                return Response(
                    {
                        "Error": "User not blocked.",
                    },
                    status=401,
                )
            user.blocked_users.remove(subject)
            # Save to connections model
            connections = Connection.objects.filter(
                Q(
                    Q(sender=user) & Q(receiver=subject)
                    | Q(Q(sender=subject) & Q(receiver=user))
                )
            )
            if connections:
                for connection in connections:
                    connection.blocked = False
                    connection.save()

            action_done = "unblock"

        elif action.lower() == "mute":
            if subject in user.muted_users.all():
                return Response(
                    {
                        "Error": "User already muted.",
                    },
                    status=401,
                )
            user.muted_users.add(subject)
            action_done = "User Muted"

        elif action.lower() == "unmute":
            if subject not in user.muted_users.all():
                return Response(
                    {
                        "Error": "User not muted.",
                    },
                    status=401,
                )
            user.muted_users.remove(subject)
            action_done = "User Unmuted"

    followers = [x.username for x in subject.followers.all()] or "No Followers"
    return Response(
        {
            "Followers Count": subject.followers.all().count(),
            "followers": followers,
            "status": 200,
            "fcm_notification_status": notification_status,
            "action": action_done,
        },
        status=200,
    )


class VerificationRequest(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=VerificationSerializer,
        responses={
            201: openapi.Response(
                "Verification request created", VerificationSerializer
            ),
            400: "Bad Request",
        },
        operation_summary="Submit a verification request",
        operation_description="Allows an authenticated user to submit a verification request.",
    )
    def post(self, request, format=None):
        serializer = VerificationSerializer(data=request.data)

        if serializer.is_valid():
            try:
                serializer.save(account_username=request.user.username)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                # Check for unique constraint violation
                if isinstance(e, IntegrityError):
                    return Response(
                        {
                            "error": "A verification request for this user already exists.",
                            "status": status.HTTP_400_BAD_REQUEST,
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                # For other exceptions, return a generic error
                return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class NotificationPreferenceViewSet(viewsets.ModelViewSet):
    queryset = NotificationSettings.objects.all()
    serializer_class = NotificationSettingsSerializers
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            return NotificationSettings.objects.filter(user=user)
        else:
            return NotificationSettings.objects.none()


class PrivacySettingsViewSet(viewsets.ModelViewSet):
    queryset = PrivacySettings.objects.all()
    serializer_class = PrivacySettingsSerializers
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            return PrivacySettings.objects.filter(user=user)
        else:
            return PrivacySettings.objects.none()

    @action(detail=False, methods=["get"], permission_classes=[])
    def blockedlist(self, request):
        user = self.request.user
        blocked = user.blocked_users.all()
        serializer = UserSerializer(blocked, many=True)
        return Response(
            {
                "blocked_users": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"], permission_classes=[])
    def mutedlist(self, request):
        user = self.request.user
        muted = user.muted_users.all()
        serializer = UserSerializer(muted, many=True)
        return Response(
            {
                "muted_users": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def toggle_private_account(self, request):
        """Toggle private account setting"""
        user = request.user
        privacy_settings, created = PrivacySettings.objects.get_or_create(user=user)
        privacy_settings.private_account = not privacy_settings.private_account
        privacy_settings.save()

        return Response(
            {
                "private_account": privacy_settings.private_account,
                "message": f"Private account {'enabled' if privacy_settings.private_account else 'disabled'}"
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def toggle_online_status(self, request):
        """Toggle show online status setting"""
        user = request.user
        privacy_settings, created = PrivacySettings.objects.get_or_create(user=user)
        privacy_settings.show_online_status = not privacy_settings.show_online_status
        privacy_settings.save()

        return Response(
            {
                "show_online_status": privacy_settings.show_online_status,
                "message": f"Show online status {'enabled' if privacy_settings.show_online_status else 'disabled'}"
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def toggle_allow_messages(self, request):
        """Toggle allow messages setting"""
        user = request.user
        privacy_settings, created = PrivacySettings.objects.get_or_create(user=user)
        privacy_settings.allow_messages = not privacy_settings.allow_messages
        privacy_settings.save()

        return Response(
            {
                "allow_messages": privacy_settings.allow_messages,
                "message": f"Allow messages {'enabled' if privacy_settings.allow_messages else 'disabled'}"
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["post"], permission_classes=[IsAuthenticated])
    def toggle_profile_views(self, request):
        """Toggle show profile views setting"""
        user = request.user
        privacy_settings, created = PrivacySettings.objects.get_or_create(user=user)
        privacy_settings.show_profile_views = not privacy_settings.show_profile_views
        privacy_settings.save()

        return Response(
            {
                "show_profile_views": privacy_settings.show_profile_views,
                "message": f"Show profile views {'enabled' if privacy_settings.show_profile_views else 'disabled'}"
            },
            status=status.HTTP_200_OK,
        )


"""
JSON FORMAT

CREATE USER:
{
  "username":"django2",
  "email":"<EMAIL>",
  "password":"Abodunshola",
  "first_name":"django",
  "last_name":"unfiltered",
  "phone_number":"+2349032530970",
  "birthday":"1999-02-19",
  "country_of_residence":"ng"
}

"""


class NotificationViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        return InappNotificationSerializer

    def get_queryset(self):
        if getattr(
            self, "swagger_fake_view", False
        ):  # drf-yasg schema generation check
            return Notification.objects.none()
        return Notification.objects.filter(recipient=self.request.user).order_by(
            "-timestamp"
        )

    @action(detail=False, methods=["post"])
    def mark_all_as_read(self, request):
        """Mark all unread notifications as read"""
        request.user.notifications.mark_all_as_read()
        return Response(
            {"status": "all notifications marked as read"}, status=status.HTTP_200_OK
        )

    @action(detail=True, methods=["post"])
    def mark_as_read(self, request, pk=None):
        """Mark a single notification as read"""
        notification = self.get_queryset().get(pk=pk)
        notification.mark_as_read()
        return Response(
            {"status": "notification marked as read"}, status=status.HTTP_200_OK
        )

    @action(detail=False, methods=["get"])
    def unread(self, request):
        """Get only unread notifications"""
        unread_notifications = request.user.notifications.unread()
        serializer = self.get_serializer(unread_notifications, many=True)
        return Response(serializer.data)
