import firebase_admin
from firebase_admin import credentials, messaging
from django.conf import settings

cred = credentials.Certificate(settings.CRED)
firebase_admin.initialize_app(cred)


def sendpush(title, msg, registration_token, dataObject):
    if not registration_token:
        # Handle the case where tokens is None or empty
        print("No tokens provided, cannot send push notifications.")
        # raise Exception(f"No Token Provided, Cannot Send Push Notifications.")
        return
    try:
        message = messaging.MulticastMessage(
            notification=messaging.Notification(title=title, body=msg),
            data=dataObject,
            tokens=registration_token,
        )
        response = messaging.send_multicast(message)
        print(response)
        return response
    except messaging.QuotaExceededError:
        raise Exception(f"Exceeded Firebase Cloud Messaging Notifications Quota")
