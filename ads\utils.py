from django.db.models import Q, F


def relevant_ads_filter(user):
    from .models import Advert

    user_interest = user.interests.choices.all()

    ad_filter = Advert.objects.filter(
        (
            Q(preferred_location_reach__country=user.country_of_residence)
            | Q(audience_interests__in=user_interest)
        ),
        Q(paid=True),
    ).annotate(post_date=F("post__post_date"))
    return ad_filter
