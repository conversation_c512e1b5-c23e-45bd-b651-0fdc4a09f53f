# Generated by Django 4.2.1 on 2024-02-28 00:55

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0012_privacysettings"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="user",
            name="mfa_hash",
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="privacysettings",
            name="comments",
            field=models.CharField(
                choices=[
                    ("everyone", "Everyone"),
                    ("no_one", "No One"),
                    ("following", "Following"),
                    ("followers_and_following", "Followers and Following"),
                ],
                default="everyone",
                max_length=256,
            ),
        ),
        migrations.AlterField(
            model_name="privacysettings",
            name="tags",
            field=models.CharField(
                choices=[
                    ("everyone", "Everyone"),
                    ("no_one", "No One"),
                    ("following", "Following"),
                    ("followers_and_following", "Followers and Following"),
                ],
                default="everyone",
                max_length=256,
            ),
        ),
    ]
