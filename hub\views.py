from django.shortcuts import get_object_or_404
from core.models import User
from post.models import Post
from post.serializers import PostSerializer
from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from .serializers import HubSerializer
from .models import Hub


class HubViewSet(viewsets.ModelViewSet):
    queryset = Hub.objects.all().order_by("name")
    serializer_class = HubSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if getattr(
            self, "swagger_fake_view", False
        ):  # drf-yasg schema generation check
            return Hub.objects.none()
        return Hub.objects.filter(user=self.request.user)

    @action(detail=False)
    def feed(self, request):
        hub_id = request.GET.get("hub_id")
        hub = get_object_or_404(Hub, id=hub_id)
        if hub.user != request.user:
            return Response(
                {"error": "You are not authorized to access this hub"},
                status=status.HTTP_403_FORBIDDEN,
            )
        posts = (
            Post.objects.filter(user__in=hub.contributors.all())
            .select_related("user")
            .order_by("-post_date")
        )
        serialized_posts = PostSerializer(posts, many=True)
        return Response(
            {f"{hub.name} posts": serialized_posts.data, "status": status.HTTP_200_OK}
        )
