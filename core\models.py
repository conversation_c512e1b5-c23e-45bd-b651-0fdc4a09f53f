from django.contrib.auth.models import AbstractUser
from django.db import models
from django.db.models.query import QuerySet
from django_countries.fields import Country<PERSON>ield
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
from cloudinary.models import CloudinaryField
from django.contrib.auth.models import BaseUserManager
from rest_framework_simplejwt.tokens import RefreshToken
from .utils import (
    VerificationStatusChoices,
    ACCOUNT_TYPE_CHOICES,
    CATEGORY_CHOICES,
    PRIVACY_CHOICES,
)

# from blogs.models import Blog


class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError("The Email field must be set")
        email = self.normalize_email(email)
        if not extra_fields.get("username"):
            user = self.model(email=email, username=email, **extra_fields)
        else:
            user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)
        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self.create_user(email, password, **extra_fields)


class InterestChoice(models.Model):
    # CREATING INTERESTS CHOICES
    name = models.CharField(max_length=50)
    thumbnail = CloudinaryField("profile-pictures", blank=True, null=True)

    def __str__(self) -> str:
        return str(self.name)


class Blog(models.Model):
    name = models.CharField(max_length=50)
    link = models.URLField()
    location = CountryField(default="NG")
    interest = models.ForeignKey(InterestChoice, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return str(self.name)


class FollowerRelation(models.Model):
    user = models.ForeignKey(
        "User", on_delete=models.CASCADE, related_name="follower_relations"
    )
    timestamp = models.DateTimeField(auto_now=True)


class User(AbstractUser):
    fcm_token = models.CharField(max_length=200, null=True, blank=True)
    bio = models.TextField(max_length=500, blank=True)
    profile_picture = CloudinaryField("profile-pictures", blank=True, null=True)
    cover_photo = CloudinaryField("cover_photos", blank=True, null=True)
    birthday = models.DateField(blank=True, null=True)
    country_of_residence = CountryField(blank=True, null=True, default="US")
    email = models.EmailField(unique=True)
    phone_number = PhoneNumberField(default="19032530970")
    is_verified = models.BooleanField(default=False)
    verification_id = models.CharField(max_length=64, blank=True)
    verification_id_created = models.DateTimeField(auto_now_add=True)
    followers = models.ManyToManyField(
        "self", related_name="following", blank=True, symmetrical=False
    )
    is_suspended = models.BooleanField(default=False)
    blocked_users = models.ManyToManyField(
        "self", related_name="blocked_by", blank=True, symmetrical=False
    )
    muted_users = models.ManyToManyField(
        "self", related_name="muted_by", blank=True, symmetrical=False
    )
    mfa_hash = models.CharField(max_length=50, null=True, blank=True)
    verification_status = models.CharField(
        max_length=3,
        choices=VerificationStatusChoices.choices,
        # default=VerificationStatusChoices.PENDING,
        null=True,
        blank=True
    )

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username"]

    objects = CustomUserManager()

    def tokens(self):
        refresh = RefreshToken.for_user(self)
        return {"refresh": str(refresh), "access": str(refresh.access_token)}

    def counts(self):
        return {
            "followers count": self.followers.count(),
            "following count": self.following.count(),
            "post count": self.post.count() if hasattr(self, "post") else 0,
        }

    def __str__(self) -> str:
        return str(self.username)


class Interest(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="interests"
    )
    choices = models.ManyToManyField(InterestChoice, blank=True)

    def __str__(self):
        return f"Interests for {self.user.username}"


class Verification(models.Model):
    # ACCOUNT_TYPE_CHOICES = [
    #     ("personal", "Personal"),
    #     ("business", "Business/Organization"),
    #     ("government", "Government"),
    #     ("official", "Government Official"),
    # ]

    # CATEGORY_CHOICES = [
    #     ("personal", "Personal"),
    #     ("politics", "Politics"),
    #     ("entertainment", "Entertainment"),
    #     ("government", "Government"),
    #     ("sports", "Sports"),
    #     ("news", "News/Media"),
    #     ("other", "Other"),
    # ]

    account_type = models.CharField(
        max_length=20,
        choices=ACCOUNT_TYPE_CHOICES.choices,
        default=ACCOUNT_TYPE_CHOICES.PERSONAL,
    )
    account_username = models.CharField(max_length=100)
    email = models.EmailField()
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES.choices,
        default=CATEGORY_CHOICES.PERSONAL,
        blank=True,
        null=True,
    )

    # Shared fields
    date_of_birth = models.DateField(blank=True, null=True)
    government_id = CloudinaryField("Government Issued ID", blank=True, null=True)
    proof_of_address = CloudinaryField("Proof of Address", blank=True, null=True)
    recent_photo = CloudinaryField("Recent Photograph", blank=True, null=True)
    reason_for_verification = models.TextField(blank=True, null=True)
    additional_info = models.TextField(blank=True, null=True)

    # Personal
    legal_name = models.CharField(max_length=100, blank=True, null=True)

    # Business
    business_name = models.CharField(max_length=100, blank=True, null=True)
    business_doc = CloudinaryField(
        "Business Registration Document", blank=True, null=True
    )
    business_address_proof = CloudinaryField(
        "Business Address Proof", blank=True, null=True
    )
    business_premises_photo = CloudinaryField(
        "Business Premises Photo", blank=True, null=True
    )

    # Government
    govt_entity_name = models.CharField(max_length=100, blank=True, null=True)
    govt_authorized_id = CloudinaryField(
        "Authorized Identification", blank=True, null=True
    )
    govt_account_verification_letter = CloudinaryField(
        "Verification Letter", blank=True, null=True
    )
    govt_registration_docs = CloudinaryField("Registration Docs", blank=True, null=True)

    # Official
    govt_agency = models.CharField(max_length=100, blank=True, null=True)
    official_id_card = CloudinaryField("Official ID Card", blank=True, null=True)
    official_letter = CloudinaryField(
        "Official Letter or Regulatory Documentation", blank=True, null=True
    )

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.account_type.title()} - {self.account_username}"

    class Meta:
        verbose_name = "Verification"
        verbose_name_plural = "Verifications"
        ordering = ["-created_at"]
        # Ensure only one verification request per user
        constraints = [
            models.UniqueConstraint(
                fields=["account_username"], name="unique_verification_per_user"
            )
        ]


class NotificationSettings(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    enable_comment_push_notifications = models.BooleanField(default=True)
    direct_messages_push_notifications = models.BooleanField(default=True)
    likes_push_notifications = models.BooleanField(default=True)
    enable_boost_push_notifications = models.BooleanField(default=True)
    tags_to_post_push_notifications = models.BooleanField(default=True)
    connections_push_notifications = models.BooleanField(default=True)
    blogs_post_push_notifications = models.BooleanField(default=True)
    direct_messages_email_notifications = models.BooleanField(default=True)
    promotional_email_from_smallworld = models.BooleanField(default=True)
    two_factor_authentication_email = models.BooleanField(default=True)
    two_factor_authentication_google_auth = models.BooleanField(default=False)

    def __str__(self):
        return str(self.user.username)


class PrivacySettings(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    comments = models.CharField(
        max_length=30, choices=PRIVACY_CHOICES.choices, default=PRIVACY_CHOICES.EVERYONE
    )
    tags = models.CharField(
        max_length=30, choices=PRIVACY_CHOICES.choices, default=PRIVACY_CHOICES.EVERYONE
    )
    private_account = models.BooleanField(null = True, default=False)
    show_online_status = models.BooleanField(null = True, default=False)
    allow_messages = models.BooleanField(null = True, default=False)
    show_profile_views = models.BooleanField(null = True, default=False)

    def __str__(self) -> str:
        return str(self.user.username)
