from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import CallOut, CallOutMedia, CallOutAction, Response as CallOutResponse
from .serializers import (
    CallOutSerializer,
    CallOutActionSerializer,
    CallOutMediaSerializer,
    ResponseSerializer,
)
from rest_framework import generics
from .serializers import FollowedUserSerializer


class FollowedUsersAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        followed_users = user.following.all()  # Get all users the current user follows
        serializer = FollowedUserSerializer(followed_users, many=True)
        return Response(serializer.data)


class CallOutAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        """List all callouts created by the user or get a specific callout by ID."""
        if pk:
            callout = get_object_or_404(CallOut, pk=pk)
            serializer = CallOutSerializer(callout)
            return Response(serializer.data, status=status.HTTP_200_OK)

        callouts = CallOut.objects.filter(user=request.user)
        serializer = CallOutSerializer(callouts, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        """Create a new callout for a followed user."""
        serializer = CallOutSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            serializer.save(user=request.user)  # Set the authenticated user
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        """Update a callout (only if the user created it)."""
        callout = get_object_or_404(CallOut, pk=pk, user=request.user)
        serializer = CallOutSerializer(
            callout, data=request.data, context={"request": request}, partial=False
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        """Partially update a callout (only if the user created it)."""
        callout = get_object_or_404(CallOut, pk=pk, user=request.user)
        serializer = CallOutSerializer(
            callout, data=request.data, context={"request": request}, partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        """Delete a callout (only if the user created it)."""
        callout = get_object_or_404(CallOut, pk=pk, user=request.user)
        callout.delete()
        return Response(
            {"message": "CallOut deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )


class CallOutActionView(generics.CreateAPIView):
    serializer_class = CallOutActionSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user
        call_out_id = self.kwargs["callout_id"]
        action = request.data.get("action")  # Should be 'agree' or 'disagree'

        if action not in ["agree", "disagree"]:
            return Response(
                {"error": "Invalid action"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            call_out = CallOut.objects.get(id=call_out_id)
        except CallOut.DoesNotExist:
            return Response(
                {"error": "CallOut not found"}, status=status.HTTP_404_NOT_FOUND
            )

        # Check if user already has an action
        existing_action = CallOutAction.objects.filter(
            user=user, call_out=call_out
        ).first()

        if existing_action:
            if existing_action.action == action:
                return Response(
                    {"message": f"You already {action}d this callout"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                existing_action.delete()  # Remove old action if switching
                new_action = CallOutAction.objects.create(
                    user=user, call_out=call_out, action=action
                )
                return Response(
                    CallOutActionSerializer(new_action).data,
                    status=status.HTTP_201_CREATED,
                )

        # Create new action if none exists
        new_action = CallOutAction.objects.create(
            user=user, call_out=call_out, action=action
        )
        return Response(
            CallOutActionSerializer(new_action).data, status=status.HTTP_201_CREATED
        )


class CallOutMediaListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, callout_id):
        """Get all media for a specific CallOut"""
        media = CallOutMedia.objects.filter(call_out_id=callout_id)
        serializer = CallOutMediaSerializer(media, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, callout_id):
        """Upload media to a specific CallOut"""
        callout = get_object_or_404(CallOut, id=callout_id)
        serializer = CallOutMediaSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(call_out=callout)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CallOutMediaDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        """Retrieve a specific media"""
        media = get_object_or_404(CallOutMedia, id=pk)
        serializer = CallOutMediaSerializer(media)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request, pk):
        """Delete a specific media"""
        media = get_object_or_404(CallOutMedia, id=pk)
        media.delete()
        return Response(
            {"message": "Media deleted successfully"}, status=status.HTTP_204_NO_CONTENT
        )


class ResponseListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, callout_id):
        """Retrieve all responses for a specific CallOut"""
        callout = get_object_or_404(CallOut, id=callout_id)
        responses = CallOutResponse.objects.filter(call_out=callout)
        serializer = ResponseSerializer(responses, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, callout_id):
        """Allow only the followed_user to respond to a CallOut"""
        callout = get_object_or_404(CallOut, id=callout_id)

        # Check if the requesting user is the one being called out
        if request.user != callout.followed_user:
            return Response(
                {"error": "You are not allowed to respond to this callout."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Serialize and save the response
        serializer = ResponseSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(
                user=request.user, call_out=callout
            )  # Auto-set user and call_out
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResponseDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, response_id):
        """Delete a response (Only the responder can delete it)"""
        response = get_object_or_404(CallOutResponse, id=response_id)
        if request.user != response.user:
            return Response(
                {"error": "You can only delete your own responses."},
                status=status.HTTP_403_FORBIDDEN,
            )

        response.delete()
        return Response(
            {"message": "Response deleted successfully."},
            status=status.HTTP_204_NO_CONTENT,
        )
