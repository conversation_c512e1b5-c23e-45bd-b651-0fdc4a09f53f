# Generated by Django 4.2.1 on 2024-03-25 22:51

from django.db import migrations
from django.db.models import F, Q


def set_is_boost(apps, schema_editor):
    Post = apps.get_model("post", "Post")
    Post.objects.filter(Q(parent__isnull=False), ~Q(user=F("parent__user"))).update(
        is_boost=True
    )


class Migration(migrations.Migration):
    dependencies = [
        ("post", "0010_post_is_boost"),
    ]

    operations = [migrations.RunPython(set_is_boost)]
