from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from analytics.models import Follower<PERSON>ount, MentionCount, AccountsPerformance
from post.models import Post
from core.models import User


class Command(BaseCommand):
    help = "Update analytics data for all users"

    def handle(self, *args, **options):
        now = timezone.now()
        yesterday = now - timedelta(days=1)

        for user in User.objects.all():
            # Follower Count (stub logic — replace with real data sources)
            FollowerCount.objects.create(user=user, count=user.followers.count())

            # Mention Count (stub logic — adjust according to your model)
            MentionCount.objects.create(user=user, count=user.mentions.count())

            # Account Performance
            posts = Post.objects.filter(user=user, post_date__gte=yesterday)
            like_count = sum(post.likes.count() for post in posts)
            comment_count = sum(post.comments.count() for post in posts)
            boost_count = posts.filter(is_boost=True).count()

            AccountsPerformance.objects.create(
                user=user,
                like_count=like_count,
                comment_count=comment_count,
                boost_count=boost_count,
                mentions_count=user.mentions.count(),  # or calculate manually if needed
            )

        self.stdout.write(self.style.SUCCESS("Analytics updated successfully"))
