from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from .models import Advert, CountryOption, AdImpression
from .serializers import AdSerializer, CountryOptionSerializer, AdImpressionSerializer
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from datetime import date
from django.db.models import Q
import requests


class AdViewSet(viewsets.ModelViewSet):
    serializer_class = AdSerializer
    permission_classes = [IsAuthenticated]  # Permission Classes so the api is secure

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            return Advert.objects.none()
        return Advert.objects.filter(advertiser=self.request.user)

    def perform_create(self, serializer):
        serializer.save(advertiser=self.request.user)

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def calculate_charge(self, request):
        # .../ads/calculate_charge?days=20

        days = request.data.get("days")
        default_charge = 1000.00
        if not days:
            return Response(
                {"error": "Days not provided"}, status=status.HTTP_400_BAD_REQUEST
            )
        charge = days * default_charge
        return Response({"charge": charge})

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def manage_ads(self, request):
        user = request.user
        today = date.today()

        ads = self.get_queryset().filter(advertiser=user)
        completed_ads = ads.filter(promotion_end_date__lt=today)
        active_ads = ads.filter(promotion_end_date__gte=today)

        serialized_completed_ads = self.serializer_class(completed_ads, many=True)
        serialized_active_ads = self.serializer_class(active_ads, many=True)

        return Response(
            {
                "completed_ads": serialized_completed_ads.data,
                "active_ads": serialized_active_ads.data,
            }
        )

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def country_options(self, request):
        country_options = CountryOption.objects.all()
        serializer = CountryOptionSerializer(country_options, many=True)
        return Response(serializer.data)

    @action(methods=["GET"], detail=False, permission_classes=[IsAuthenticated])
    def ad_feed(self, request):
        user = request.user
        today = date.today()

        # Get active ads that haven't expired
        ads = Advert.objects.filter(paid=True, promotion_end_date__gte=today).order_by(
            "-created_at"
        )

        # Filter by user's country_of_residence if specified in ad targeting
        user_country_of_residence = user.country_of_residence
        if user_country_of_residence:
            ads = ads.filter(
                Q(preferred_location_reach__isnull=True)
                | Q(  # No country_of_residence targeting
                    preferred_location_reach__code=user_country_of_residence
                )  # User's location matches one of the preferred locations
            )
        serializer = self.serializer_class(ads, many=True)
        return Response(serializer.data)

    @action(methods=["POST"], detail=True, permission_classes=[IsAuthenticated])
    def ad_impression(self, request, pk=None):
        """Record an impression when a user views an ad"""
        try:
            ad = get_object_or_404(Advert, pk=pk)
            viewer = request.user

            # Create impression if it doesn't exist
            impression, created = AdImpression.objects.get_or_create(
                ad=ad, viewer=viewer
            )

            if created:
                return Response(
                    {"message": "Ad impression recorded successfully"},
                    status=status.HTTP_201_CREATED,
                )
            return Response(
                {"message": "Ad impression already exists"}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(methods=["GET"], detail=True, permission_classes=[IsAuthenticated])
    def ad_impressions(self, request, pk=None):
        """Get all impressions for an ad"""
        try:
            ad = get_object_or_404(Advert, advertiser=request.user, pk=pk)

            impressions = ad.impressions.all()
            total_impressions = ad.impressions.count()
            serializer = AdImpressionSerializer(impressions, many=True)
            return Response(
                {"Total Count": total_impressions, "All Impressions": serializer.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class VerifyTransactions(APIView):
    # Store API constants as class variables to avoid repetition and make updates easier
    PAYSTACK_BASE_URL = "https://api.paystack.co"
    PAYSTACK_SECRET_KEY = "sk_test_d19b1d39db004f3174622e60a0f6d740d3a26e47"

    def _get_paystack_headers(self):
        # Extracted headers to separate method for reusability and consistency
        # Added Bearer prefix to follow OAuth2 convention
        return {
            "Authorization": f"Bearer {self.PAYSTACK_SECRET_KEY}",
            "Content-Type": "application/json",
        }

    def _verify_transaction(self, reference):
        # Extracted Paystack API call to separate method to:
        # 1. Improve testability
        # 2. Follow single responsibility principle
        # 3. Make the code more maintainable
        url = f"{self.PAYSTACK_BASE_URL}/transaction/verify/{reference}"
        response = requests.get(url, headers=self._get_paystack_headers())
        response.raise_for_status()
        return response.json()

    def get(self, request, reference, ad_id):
        # Main endpoint handler is now more focused on business logic
        # rather than implementation details
        ad = get_object_or_404(Advert, pk=ad_id)

        try:
            transaction_data = self._verify_transaction(reference)

            if transaction_data["data"]["status"] == "success":
                ad.paid = True
                ad.save()
                return Response(
                    {
                        "message": "Transaction verified successfully",
                        "transaction_data": transaction_data,
                    },
                    status=status.HTTP_200_OK,  # Added explicit 200 status
                )

            return Response(
                {"message": "Transaction failed"}, status=status.HTTP_400_BAD_REQUEST
            )

        except requests.RequestException as e:
            # Added error details to response for better debugging
            return Response(
                {"message": f"Error verifying transaction: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
