from django.db.models.signals import post_save
from django.dispatch import receiver
from analytics.models import MentionCount
from .models import Mention
from django.utils import timezone
from datetime import datetime

@receiver(post_save, sender=Mention)
def increment_mention_count(sender, instance, created, **kwargs):
    print(f"Signal fired for Mention: id={instance.id}, user={instance.user}, created={created}")
    if not created:
        return
    today = timezone.now().date()
    start_of_day = timezone.make_aware(datetime.combine(today, datetime.min.time()))
    end_of_day = timezone.make_aware(datetime.combine(today, datetime.max.time()))
    mention_count = MentionCount.objects.filter(
        user=instance.user,
        timestamp__gte=start_of_day,
        timestamp__lte=end_of_day
    ).first()
    if mention_count is None:
        MentionCount.objects.create(user=instance.user, count=1)
    else:
        mention_count.count += 1
        mention_count.save()
