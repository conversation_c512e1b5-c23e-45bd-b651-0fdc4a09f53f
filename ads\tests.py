from django.test import TestCase
from rest_framework.test import APIClient
from django.urls import reverse
from django.contrib.auth import get_user_model
from core.models import InterestChoice
from post.models import Post
from .models import LocationPreferences


class AdsTestCase(TestCase):
    def setUp(self):
        User = get_user_model()
        self.user_a = User.objects.create_user(
            email="<EMAIL>", password="testpassa", fcm_token="token"
        )
        self.post = Post.objects.create(user=self.user_a, content="New Content")
        self.interest = InterestChoice.objects.create(name="Books")
        self.location = LocationPreferences.objects.create(state="MA")

    def get_client(self):
        client = APIClient()
        client.force_authenticate(user=self.user_a)
        return client

    def test_create_user_endpoint(self):
        client = self.get_client()
        url = "https://www.smallclosedworld.com/api/v1/ads/"
        response = client.post(
            url,
            {
                "promotion_start_date": "2019-08-24",
                "promotion_end_date": "2019-08-24",
                "age_range_min": 21,
                "age_range_max": 27,
                "ad_button": "send_dm",
                "paid": True,
                "post": 1,
                "advertiser": 1,
                "preferred_location_reach": [1],
                "audience_interests": [1],
            },
            format="json",
        )
        # response_data = response.json()
        print(response.json())
        self.assertEqual(response.status_code, 201)
