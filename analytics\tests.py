from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from core.models import User
from .models import FollowerCount, MentionCount, AccountsPerformance, ProfileViewTrack
from post.models import Post
from datetime import timedelta
from django.utils import timezone


class FollowerCountViewTest(TestCase):
    def setUp(self):
        # Create test user and log them in
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Create FollowerCount entries for the user
        FollowerCount.objects.create(
            user=self.user, timestamp=timezone.now() - timedelta(days=1), count=150
        )
        FollowerCount.objects.create(
            user=self.user, timestamp=timezone.now() - timedelta(days=5), count=200
        )

    def test_get_follower_count_last_7_days(self):
        response = self.client.get(
            "/api/v1/follower_count/", {"date_range_option": "last_7_days"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 1
        )  # Adjust this based on your expected result

    def test_get_follower_count_custom_range(self):
        start_date = (timezone.now() - timedelta(days=6)).date()
        end_date = timezone.now().date()
        response = self.client.get(
            "/api/v1/follower_count/",
            {
                "date_range_option": "custom_range",
                "start_date": start_date,
                "end_date": end_date,
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 1
        )  # Adjust this based on your expected result

    def test_get_follower_count_invalid_date(self):
        response = self.client.get(
            "/api/v1/follower_count/",
            {
                "date_range_option": "custom_range",
                "start_date": "invalid-date",
                "end_date": "invalid-date",
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class MentionCountViewTest(TestCase):
    def setUp(self):
        # Create test user and log them in
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Create MentionCount entries for the user
        MentionCount.objects.create(user=self.user, count=5)
        MentionCount.objects.create(user=self.user, count=10)

    def test_get_mention_count(self):
        response = self.client.get("/api/v1/mention_count/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 2
        )  # Adjust this based on your expected result


class AccountPerformanceViewTest(TestCase):
    def setUp(self):
        # Create test user and log them in
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Create AccountsPerformance entries for the user
        AccountsPerformance.objects.create(
            user=self.user, timestamp=timezone.now() - timedelta(days=1), performance=90
        )
        AccountsPerformance.objects.create(
            user=self.user, timestamp=timezone.now() - timedelta(days=5), performance=80
        )

    def test_get_account_performance_last_30_days(self):
        response = self.client.get(
            "/api/v1/account_performance/", {"date_range_option": "last_30_days"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 2
        )  # Adjust this based on your expected result

    def test_get_account_performance_invalid_date(self):
        response = self.client.get(
            "/api/v1/account_performance/",
            {
                "date_range_option": "custom_range",
                "start_date": "invalid-date",
                "end_date": "invalid-date",
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class ProfileViewTrackViewTest(TestCase):
    def setUp(self):
        # Create test user and log them in
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Create ProfileViewTrack entries for the user
        ProfileViewTrack.objects.create(
            user=self.user, timestamp=timezone.now() - timedelta(days=1), views=10
        )
        ProfileViewTrack.objects.create(
            user=self.user, timestamp=timezone.now() - timedelta(days=5), views=20
        )

    def test_get_profile_view_track(self):
        response = self.client.get(
            "/api/v1/profile_view_track/", {"date_range_option": "last_7_days"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 1
        )  # Adjust this based on your expected result

    def test_get_profile_view_track_invalid_date(self):
        response = self.client.get(
            "/api/v1/profile_view_track/",
            {
                "date_range_option": "custom_range",
                "start_date": "invalid-date",
                "end_date": "invalid-date",
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class PostPerformanceTest(TestCase):
    def setUp(self):
        # Create test user and log them in
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password"
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        # Create Post entries for the user
        Post.objects.create(
            user=self.user,
            post_date=timezone.now() - timedelta(days=1),
            content="Test post 1",
            likes=5,
            comments=3,
        )
        Post.objects.create(
            user=self.user,
            post_date=timezone.now() - timedelta(days=3),
            content="Test post 2",
            likes=15,
            comments=10,
        )

    def test_get_post_performance(self):
        response = self.client.get(
            "/api/v1/post_performance/", {"date_range_option": "last_7_days"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            len(response.data), 2
        )  # Adjust this based on your expected result

    def test_get_post_performance_invalid_date(self):
        response = self.client.get(
            "/api/v1/post_performance/",
            {
                "date_range_option": "custom_range",
                "start_date": "invalid-date",
                "end_date": "invalid-date",
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
