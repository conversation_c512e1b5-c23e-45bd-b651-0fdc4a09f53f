from django.urls import path
from .views import (
    CallOutAPIView,
    FollowedUsersAPIView,
    CallOutActionView,
    CallOutMediaListView,
    CallOutMediaDetailView,
    ResponseListCreateView,
    ResponseDetailView,
)


urlpatterns = [
    path("callout/", CallOutAPIView.as_view(), name="callout-list-create"),
    path(
        "callout/<int:pk>/",
        CallOutAPIView.as_view(),
        name="callout-detail-update-delete",
    ),
    path("followed-users/", FollowedUsersAPIView.as_view(), name="followed-users"),
    path(
        "callouts/<int:callout_id>/action/",
        CallOutActionView.as_view(),
        name="callout-action",
    ),
    path(
        "callouts/<int:callout_id>/media/",
        CallOutMediaListView.as_view(),
        name="callout-media-list",
    ),
    path(
        "callout-media/<int:pk>/",
        CallOutMediaDetailView.as_view(),
        name="callout-media-detail",
    ),
    path(
        "callouts/<int:callout_id>/responses/",
        ResponseListCreateView.as_view(),
        name="callout-responses",
    ),
    path(
        "responses/<int:response_id>/",
        ResponseDetailView.as_view(),
        name="response-detail",
    ),
]
