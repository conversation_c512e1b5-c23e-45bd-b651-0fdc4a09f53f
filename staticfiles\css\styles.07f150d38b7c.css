/* Django Unfold Admin Custom Styles */

/* Form Fields */
.unfold-form-control {
    @apply rounded-lg border-gray-300 shadow-sm;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.unfold-form-control:focus {
    @apply border-blue-500 ring-2 ring-blue-200;
    outline: none;
}

/* Form Labels */
.unfold-form-label {
    @apply text-sm font-medium text-gray-700 mb-1;
}

/* Buttons */
.unfold-button {
    @apply inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    transition: all 0.2s ease-in-out;
}

.unfold-button-secondary {
    @apply bg-white border-gray-300 text-gray-700 hover:bg-gray-50;
}

/* Cards and Panels */
.unfold-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200;
    transition: box-shadow 0.2s ease-in-out;
}

.unfold-card:hover {
    @apply shadow-md;
}

/* Tables */
.unfold-table {
    @apply min-w-full divide-y divide-gray-200;
}

.unfold-table th {
    @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.unfold-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.unfold-table tr:nth-child(even) {
    @apply bg-gray-50;
}

/* Dashboard Widgets */
.unfold-widget {
    @apply p-6 bg-white rounded-xl shadow-sm border border-gray-200;
}

.unfold-widget-title {
    @apply text-lg font-medium text-gray-900 mb-4;
}

/* Navigation */
.unfold-nav-item {
    @apply px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100;
    transition: all 0.2s ease-in-out;
}

.unfold-nav-item.active {
    @apply bg-blue-50 text-blue-700;
}

/* Alerts and Messages */
.unfold-alert {
    @apply rounded-lg p-4 mb-4;
}

.unfold-alert-success {
    @apply bg-green-50 text-green-700 border border-green-200;
}

.unfold-alert-error {
    @apply bg-red-50 text-red-700 border border-red-200;
}

/* Pagination */
.unfold-pagination {
    @apply flex items-center justify-center mt-4;
}

.unfold-page-link {
    @apply px-3 py-2 mx-1 rounded-md text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50;
}

.unfold-page-link.active {
    @apply bg-blue-600 text-white border-blue-600;
}

/* Select Fields */
select.unfold-form-control {
    @apply pl-3 pr-10 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md;
}

/* Checkboxes and Radio Buttons */
.unfold-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.unfold-radio {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

/* File Upload */
.unfold-file-upload {
    @apply flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg;
}

/* Date and Time Inputs */
input[type="date"].unfold-form-control,
input[type="time"].unfold-form-control,
input[type="datetime-local"].unfold-form-control {
    @apply pl-3 pr-10;
}

/* Tooltips */
.unfold-tooltip {
    @apply absolute z-10 p-2 text-sm text-white bg-gray-900 rounded-lg opacity-0 invisible;
    transition: opacity 0.2s ease-in-out;
}

.unfold-tooltip.visible {
    @apply opacity-100 visible;
}

/* Rich Text Editor */
.unfold-rich-text-editor {
    @apply mt-1 block w-full rounded-lg border-gray-300 shadow-sm;
    min-height: 120px;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
    .unfold-form-control,
    .unfold-button {
        @apply w-full;
    }
    
    .unfold-table {
        @apply table-auto;
    }
    
    .unfold-widget {
        @apply p-4;
    }
}
