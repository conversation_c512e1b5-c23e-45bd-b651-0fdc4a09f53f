# Generated by Django 4.2.1 on 2025-06-17 10:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("post", "0024_mention_is_read"),
    ]

    operations = [
        migrations.CreateModel(
            name="PostBoost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="postboosts",
                        to="post.post",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="postboost",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "post")},
            },
        ),
        migrations.AddField(
            model_name="post",
            name="boosted_by",
            field=models.ManyToManyField(
                blank=True,
                related_name="boosted_posts",
                through="post.PostBoost",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
