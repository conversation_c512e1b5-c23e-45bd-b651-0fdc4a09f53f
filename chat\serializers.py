from rest_framework import serializers
from .models import Message, Connection
from core.models import User
from core.serializers import UserSerializer
from post.serializers import PostSerializer


class MessageSerializer(serializers.ModelSerializer):
    is_author = serializers.SerializerMethodField()
    post = PostSerializer(read_only=True)

    class Meta:
        model = Message
        fields = [
            "id",
            "is_author",
            "text",
            "is_read",
            "post",
            "image",
            "video",
            "created",
            "updated",
        ]

    def get_is_author(self, obj):
        return self.context["user"] == obj.user


class SearchSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ["email", "username", "first_name", "last_name", "status"]

    def get_status(self, obj):
        if obj.pending_them:
            return "pending-them"
        elif obj.pending_me:
            return "pending-me"
        elif obj.connected:
            return "connected"
        else:
            return "no connection"


class RequestSerializer(serializers.ModelSerializer):
    sender = UserSerializer()
    receiver = UserSerializer()

    class Meta:
        model = Connection
        fields = ["id", "sender", "receiver", "created"]


class FriendSerializer(serializers.ModelSerializer):
    friend = serializers.SerializerMethodField()
    preview = serializers.SerializerMethodField()
    updated = serializers.SerializerMethodField()
    connection_id = serializers.SerializerMethodField()

    class Meta:
        model = Connection
        fields = ["connection_id", "friend", "preview", "updated"]

    def get_connection_id(self, obj):
        return obj.id

    def get_friend(self, obj):
        # if im the sender
        if self.context["user"] == obj.sender:
            return UserSerializer(obj.receiver).data
        elif self.context["user"] == obj.receiver:
            return UserSerializer(obj.sender).data
        else:
            print("no user found in friend serializer")

    def get_preview(self, obj):
        default = "New Connection"
        if not hasattr(obj, "latest_text"):
            return default
        return obj.latest_text or default

    def get_updated(self, obj):
        if not hasattr(obj, "latest_created"):
            date = obj.updated
        else:
            date = obj.latest_created or obj.updated
        return date.isoformat()
