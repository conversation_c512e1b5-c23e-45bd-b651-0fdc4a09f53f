import pytest
from unittest import mock
from django.urls import reverse
from core.models import User
from .models import Post
from notifications.models import Notification
from rest_framework.test import APIClient


@pytest.fixture
def user_a(db):
    return User.objects.create_user(
        email="<EMAIL>", password="testpassa", fcm_token=["token"]
    )


@pytest.fixture
def user_b(db):
    return User.objects.create_user(email="<EMAIL>", password="testpassb")


@pytest.fixture
def first_post(user_a, db):
    return Post.objects.create(user=user_a, content="New Post")


@pytest.fixture
def second_post(user_b, db):
    return Post.objects.create(user=user_b, content="Post2")


@pytest.fixture
def api_client(user_a):
    client = APIClient()
    client.force_authenticate(user_a)
    return client


@pytest.mark.django_db
def test_created_post(first_post, second_post):
    assert Post.objects.count() == 2


@pytest.mark.django_db
def test_list_create_post_api_endpoint(api_client):
    create_response = api_client.post(
        reverse("post:posts-list"), {"content": "new post"}
    )
    assert create_response.status_code == 201

    list_response = api_client.get(reverse("post:posts-list"))
    assert list_response.status_code == 200

    create_response_data = create_response.json()
    list_response_data = list_response.json()

    assert "content" in create_response_data  # Ensure 'content' exists
    assert create_response_data["content"] == "new post"
    assert any(post.get("content") == "new post" for post in list_response_data)


@pytest.mark.django_db
def test_post_feed_endpoint(api_client):
    response = api_client.get(reverse("post:post-feed"))  # Changed POST to GET
    assert response.status_code == 200
    assert isinstance(response.json(), list)  # Ensure response is a list


@mock.patch("firebase_admin.messaging.send")
@pytest.mark.django_db
def test_post_actions_endpoint(mock_send, api_client, first_post):
    like_response = api_client.post(
        reverse("post:post-actions"), {"post_id": first_post.id, "action": "like"}
    )
    boost_response = api_client.post(
        reverse("post:post-actions"), {"post_id": first_post.id, "action": "boost"}
    )

    assert like_response.status_code == 200
    assert boost_response.status_code == 200

    assert like_response.json()["status"] == "Liked"
    assert boost_response.json()["status"] == "Boosted"

    assert Notification.objects.count() == 2


@pytest.mark.django_db
def test_unauthorized_user_cannot_access_protected_endpoints():
    client = APIClient()
    response = client.get(reverse("post:posts-list"))
    assert response.status_code == 401


@pytest.mark.django_db
def test_post_cannot_be_created_without_content(api_client):
    response = api_client.post(reverse("post:posts-list"), {"content": ""})
    assert response.status_code == 400  # Ensure backend validation exists


@pytest.mark.django_db
def test_post_can_be_deleted(api_client, first_post):
    response = api_client.delete(reverse("post:posts-detail", args=[first_post.id]))
    assert response.status_code == 204
    assert not Post.objects.filter(id=first_post.id).exists()


@pytest.mark.django_db
def test_invalid_actions_return_proper_errors(api_client, first_post):
    response = api_client.post(
        reverse("post:post-actions"),
        {"post_id": first_post.id, "action": "invalid_action"},
    )
    assert response.status_code == 400


@pytest.mark.django_db
def test_post_can_be_updated(api_client, first_post):
    response = api_client.patch(
        reverse("post:posts-detail", args=[first_post.id]),
        {"content": "Updated content"},
    )
    assert response.status_code == 200
    assert response.json()["content"] == "Updated content"


@mock.patch("firebase_admin.messaging.send")
@pytest.mark.django_db
def test_post_cannot_be_boosted_twice(mock_send, api_client, first_post):
    api_client.post(
        reverse("post:post-actions"), {"post_id": first_post.id, "action": "boost"}
    )
    response = api_client.post(
        reverse("post:post-actions"), {"post_id": first_post.id, "action": "boost"}
    )
    assert response.status_code == 400  # Ensure proper validation is in place
