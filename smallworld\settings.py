"""
Django settings for smallworld project.

Generated by 'django-admin startproject' using Django 4.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
from datetime import timed<PERSON><PERSON>
from decouple import config, UndefinedValueError
import django_heroku
import dj_database_url
import os, sys
from pathlib import Path

# Setup config to read from .env file in base directory
BASE_DIR = Path(__file__).resolve().parent.parent

# cloudinary imports
import cloudinary
import cloudinary.uploader
import cloudinary.api

REQUIRED_ENV_VARS = {
    "SECRET_KEY": str,
    "DEBUG": bool,
    "CORS_ALLOWED_ORIGINS": list,
    "CSRF_TRUSTED_ORIGINS": list,
    "CSRF_COOKIE_SECURE":bool,
    "ALLOWED_HOSTS": list,
    "DATABASE_URL": str,
    "REDIS_HOST": str,
    "REDIS_PORT": int,
    "REDIS_URL": str,
    "PAYSTACK_SECRET_KEY": str,
    "PAYSTACK_PUBLIC_KEY": str,
    "CLOUDINARY_CLOUD_NAME": str,
    "CLOUDINARY_API_KEY": str,
    "CLOUDINARY_API_SECRET": str,
    "WEBSITE_URL": str,
}

#Exit early if testing to skip .env check
if not ("pytest" in sys.modules or "test" in sys.argv):
    missing_or_invalid = []

    for key, expected_type in REQUIRED_ENV_VARS.items():
        try:
            value=config(key)
            #validate the type by casting(str, int, list)
            if expected_type == bool:
                value = config(key, cast=bool)
            elif expected_type == int:
                value = config(key, cast=int)
            elif expected_type == list:
                value = config(key, cast=list)
            elif expected_type == str:
                value=config(key, cast=str)
        except UndefinedValueError:
            missing_or_invalid.append(f"Missing:{key}")
        except Exception as e:
            missing_or_invalid.append(f"Malformed: {key} ({str(e)})")

    
    if missing_or_invalid:
        print("\n Environment Configuration Error:")

        for issue in missing_or_invalid:
            print(f" -{issue}")
        print("\n Please check your .env file.")
        sys.exit(1) #Stop Django Startup
from celery.schedules import crontab

from django.templatetags.static import static
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _



# Database configuration
DATABASE_URL = config("DATABASE_URL")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config("DEBUG", cast=bool, default=False)

# Parse ALLOWED_HOSTS from env and ensure proper formatting
ALLOWED_HOSTS = config(
    "ALLOWED_HOSTS", default="*", cast=lambda v: [s.strip() for s in v.split(",")]
)
WEBSITE_URL = config("WEBSITE_URL")

# Daphne
ASGI_APPLICATION = "smallworld.asgi.application"
# Set up Redis connection details
REDIS_HOST = config("REDIS_HOST", default="localhost")
REDIS_PORT = config("REDIS_PORT", default=6379, cast=int)
REDIS_PASSWORD = config("REDIS_PASSWORD", default=None)

# Use Redis as the default cache backend
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{REDIS_HOST}:{REDIS_PORT}/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": REDIS_PASSWORD,
        },
    }
}


# Use Redis as the Channels layer (if you're using Channels)
if "CHANNEL_LAYERS" not in locals():
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels_redis.core.RedisChannelLayer",
            "CONFIG": {
                "hosts": [config("REDIS_URL")],
            },
        },
    }


# Celery Configuration

CELERY_BROKER_URL = config("REDIS_URL")
CELERY_RESULT_BACKEND = config("REDIS_URL")
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "Africa/Lagos"


# Application definition
INSTALLED_APPS = [
    # Admin UI Package
    "unfold", 

    # ASGI SERVER
    "daphne",
    # INBUILT APPS
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    # EXTERNAL PACKAGES
    "cloudinary",
    "drf_yasg",
    "notifications",
    "notifications_rest",
    "rest_framework",
    "django_filters",
    "django_celery_beat",
    # INTERNAL APPS
    "ads",
    "analytics",
    # "blogs",
    "chat",
    "core",
    "explore",
    "hub",
    "payments",
    "post",
    "support",
    "callout",
]


MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

UNFOLD = {
    # Site branding
    "SITE_TITLE": "SmallWorld Admin",
    "SITE_HEADER": "Manage SmallWorld Admin",
    "SITE_SUBTITLE": "SmallWorld Admin Dashboard",
    "INDEX_TITLE": "SmallWorld Admin Dashboard",
    "SITE_URL": "/",
    "SITE_ICON": lambda request: static("img/logo2.png"),
    "SITE_LOGO": lambda request: static("img/logo2.png"),
    "SITE_SYMBOL": "speed",
    "environment": "production",  # Set to 'production' in production environment
    
    # Features
    "SHOW_HISTORY": True,
    
    # Login configuration 
    "LOGIN": {
        "redirect_after": lambda request: reverse_lazy("admin:index"),
        "centered": True,
    },
    
    # Assets
    "STYLES": [lambda request: static("css/styles.css")],
    # "SCRIPTS": [lambda request: static("js/script.js")],
    
    # Theme colors
    "COLORS": {
        "primary": {
            "50": "239 246 255",
            "100": "219 234 254", 
            "200": "191 219 254",
            "300": "147 197 253",
            "400": "96 165 250",
            "500": "0 84 203",    # Main brand color
            "600": "0 74 179",
            "700": "0 64 155",
            "800": "0 54 131",
            "900": "0 44 107",
            "950": "0 34 83",
        }
    },
    
    # Sidebar configuration
    "SIDEBAR": {
        "show_search": True,
    }
}

ROOT_URLCONF= "smallworld.urls"

AUTH_USER_MODEL = "core.User"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(BASE_DIR, "templates"),
            os.path.join(BASE_DIR, "templates/django/forms"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {"anon": "1000/day", "user": "1000/day"},
}


WSGI_APPLICATION = "smallworld.wsgi.application"







# if "pytest" in sys.modules or "test" in sys.argv:
#     DATABASES = {
#         "default": {
#             "ENGINE": "django.db.backends.sqlite3",
#             "NAME": BASE_DIR / "test_db.sqlite3",  # Use a separate test DB
#             "TEST": {
#                 "NAME": BASE_DIR / "test_db.sqlite3",
#             },
#         }
#     }
# else:
#     DATABASES = {
#         "default": dj_database_url.config(default=DATABASE_URL, conn_max_age=1000),
#     }
#     DATABASES["default"]["OPTIONS"] = {"connect_timeout": 10}







# DATABASES = {
#         "default": {
#             "ENGINE": "django.db.backends.sqlite3",
#             "NAME": BASE_DIR / "db.sqlite3",  # Use a separate test DB
#             "TEST": {
#                 "NAME": BASE_DIR / "db.sqlite3",
#             },
#         }
#     }


AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "core.auth_backends.EmailBackend",
]

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True


USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/


STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles/")
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

MEDIA_ROOT = os.path.join(BASE_DIR, "media")


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=20),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=15),
}

TWEET_ACTION_OPTIONS = ["like", "unlike", "boost"]

# SPECTACULAR_SETTINGS = {
#     "TITLE": "SmallClosedWorld API",
#     "DESCRIPTION": "Your project description",
#     "VERSION": "1.0.0",
#     "SERVE_INCLUDE_SCHEMA": False,
#     # OTHER SETTINGS
# }

django_heroku.settings(locals())

# Cloudinary - Django Integration

cloudinary.config(
    cloud_name=config("CLOUDINARY_CLOUD_NAME"),
    api_key=config("CLOUDINARY_API_KEY"),
    api_secret=config("CLOUDINARY_API_SECRET"),
)

PAYSTACK_SECRET_KEY = config("PAYSTACK_SECRET_KEY")
PAYSTACK_PUBLIC_KEY = config("PAYSTACK_PUBLIC_KEY")

CRED = f"{BASE_DIR}/credentials.json"


NOTIFICATIONS_REST = {
    "PAGINATION_CLASS": "notifications_rest.pagination.NotificationsPagination",
    "FILTER_BACKEND": "notifications_rest.filters.FilterNotifications",
}
CORS_ALLOWED_ORIGINS = config("CORS_ALLOWED_ORIGINS", cast=lambda v: [s.strip() for s in v.split(",")] if v else [])
CSRF_TRUSTED_ORIGINS = config("CSRF_TRUSTED_ORIGINS", cast=lambda v: [s.strip() for s in v.split(",")] if v else [])
CSRF_COOKIE_SECURE = config("CSRF_COOKIE_SECURE", default=True, cast=bool)

# Celery Configuration Options
# CELERY_TIMEZONE = "Australia/Tasmania"
# CELERY_TASK_TRACK_STARTED = True
# CELERY_TASK_TIME_LIMIT = 30 * 60

# PG BOUNCER
DISABLE_SERVER_SIDE_CURSORS = (
    True  # required when using pgbouncer's pool_mode=transaction
)



django_heroku.settings(locals())

import sys
if "test" in sys.argv or "pytest" in sys.modules:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "test_db.sqlite3",
        }
    }