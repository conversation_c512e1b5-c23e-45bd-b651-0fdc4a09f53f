from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from core.models import User
from post.models import Post
from explore.models import TrendingHashtag, TrendingTopics
from explore.serializers import SearchSerializer

User = get_user_model()


class SearchViewTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        # Create test users
        self.user1 = User.objects.create_user(
            username="testuser1", email="<EMAIL>", password="testpass123"
        )
        self.user2 = User.objects.create_user(
            username="johnsmith", email="<EMAIL>", password="testpass123"
        )
        self.user3 = User.objects.create_user(
            username="janedoe", email="<EMAIL>", password="testpass123"
        )
        self.blocked_user = User.objects.create_user(
            username="blockeduser", email="<EMAIL>", password="testpass123"
        )
        self.muted_user = User.objects.create_user(
            username="muteduser", email="<EMAIL>", password="testpass123"
        )

        # Set up relationships
        self.user1.blocked_users.add(self.blocked_user)
        self.user1.muted_users.add(self.muted_user)

        # Create test posts
        self.post1 = Post.objects.create(
            user=self.user2, content="This is a python programming tutorial"
        )
        self.post2 = Post.objects.create(
            user=self.user3, content="Learning Django framework"
        )
        self.blocked_post = Post.objects.create(
            user=self.blocked_user,
            content="This post should not appear in python searches",
        )
        self.muted_post = Post.objects.create(
            user=self.muted_user, content="Muted user talking about python"
        )

    def test_search_requires_auth(self):
        """Test that search requires authentication"""
        url = reverse("search")
        response = self.client.post(url, {"keyword": "test"}, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    # Other search functionality tests removed


class TrendingHashtagsViewTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        # Create trending hashtags
        self.hashtag1 = TrendingHashtag.objects.create(
            hashtag="#python", occurrence=100
        )
        self.hashtag2 = TrendingHashtag.objects.create(hashtag="#django", occurrence=50)

    def test_authentication_required(self):
        """Test that authentication is required"""
        url = reverse("trending_hashtag")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_trending_hashtags(self):
        """Test retrieving trending hashtags"""
        self.client.force_authenticate(user=self.user)
        url = reverse("trending_hashtag")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        hashtags = [item["hashtag"] for item in response.data]
        self.assertIn("#python", hashtags)
        self.assertIn("#django", hashtags)


class TrendingTopicsViewTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        # Create trending topics
        self.topic1 = TrendingTopics.objects.create(topic="Technology", occurrence=200)
        self.topic2 = TrendingTopics.objects.create(topic="Sports", occurrence=150)

    def test_authentication_required(self):
        """Test that authentication is required"""
        url = reverse("trending_topics")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_trending_topics(self):
        """Test retrieving trending topics"""
        self.client.force_authenticate(user=self.user)
        url = reverse("trending_topics")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        topics = [item["topic"] for item in response.data]
        self.assertIn("Technology", topics)
        self.assertIn("Sports", topics)


class PeopleYouMayKnowTestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        # Create test users
        self.user1 = User.objects.create_user(
            username="user1", email="<EMAIL>", password="testpass123"
        )
        self.user2 = User.objects.create_user(
            username="user2", email="<EMAIL>", password="testpass123"
        )
        self.user3 = User.objects.create_user(
            username="user3", email="<EMAIL>", password="testpass123"
        )
        self.user4 = User.objects.create_user(
            username="user4", email="<EMAIL>", password="testpass123"
        )
        self.blocked_user = User.objects.create_user(
            username="blockeduser", email="<EMAIL>", password="testpass123"
        )

        # Set up main test user (user1) relationships
        # user1 follows user2, who follows user3 and user4
        self.user1.following.add(self.user2)
        self.user2.following.add(self.user3, self.user4)

        # Block relationship
        self.user1.blocked_users.add(self.blocked_user)
        # Blocked user follows user2 (but should not be in suggestions)

    def test_authentication_required(self):
        """Test that authentication is required"""
        url = reverse("suggested_user")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_suggested_users(self):
        """Test retrieving suggested users"""
        self.client.force_authenticate(user=self.user1)
        url = reverse("suggested_user")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        suggested_usernames = [user["username"] for user in response.data]

        # Should not contain blocked users or self
        self.assertNotIn("blockeduser", suggested_usernames)
        self.assertNotIn("user1", suggested_usernames)

    def test_excludes_self_from_suggestions(self):
        """Test that the requesting user is not included in suggestions"""
        self.client.force_authenticate(user=self.user1)
        url = reverse("suggested_user")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        suggested_usernames = [user["username"] for user in response.data]
        self.assertNotIn("user1", suggested_usernames)

    def test_excludes_blocked_users(self):
        """Test that blocked users are not suggested"""
        self.client.force_authenticate(user=self.user1)
        url = reverse("suggested_user")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        suggested_usernames = [user["username"] for user in response.data]
        self.assertNotIn("blockeduser", suggested_usernames)

    def test_no_duplicates_in_suggestions(self):
        """Test that there are no duplicate users in suggestions"""
        # Create additional following relationships to potentially create duplicates
        self.user1.following.add(self.user3)  # user1 now follows both user2 and user3

        self.client.force_authenticate(user=self.user1)
        url = reverse("suggested_user")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        suggested_usernames = [user["username"] for user in response.data]
        # Check that there are no duplicates
        self.assertEqual(len(suggested_usernames), len(set(suggested_usernames)))

    def test_empty_suggestions_for_user_with_no_following(self):
        """Test suggestions for user who follows nobody"""
        new_user = User.objects.create_user(
            username="newuser", email="<EMAIL>", password="testpass123"
        )

        self.client.force_authenticate(user=new_user)
        url = reverse("suggested_user")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)


class SearchSerializerTestCase(TestCase):
    """Test the search serializer if needed"""

    def test_search_serializer_validation(self):
        """Test search serializer with valid data"""

        data = {"keyword": "test"}
        serializer = SearchSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_search_serializer_missing_keyword(self):
        """Test search serializer with missing keyword"""

        data = {}
        serializer = SearchSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn("keyword", serializer.errors)


class IntegrationTestCase(APITestCase):
    """Integration tests combining multiple views"""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

    def test_all_endpoints_require_authentication(self):
        """Test that all endpoints require authentication"""
        endpoints = [
            reverse("search"),
            reverse("trending_hashtag"),
            reverse("trending_topics"),
            reverse("suggested_user"),
        ]

        for endpoint in endpoints:
            response = self.client.get(endpoint)
            self.assertEqual(
                response.status_code,
                status.HTTP_401_UNAUTHORIZED,
                f"Endpoint {endpoint} should require authentication",
            )

    def test_all_authenticated_endpoints_return_success(self):
        """Test that all endpoints return success when authenticated"""
        self.client.force_authenticate(user=self.user)

        # Test endpoints that don't require parameters
        endpoints = [
            reverse("trending_hashtag"),
            reverse("trending_topics"),
            reverse("suggested_user"),
        ]

        for endpoint in endpoints:
            response = self.client.get(endpoint)
            self.assertIn(
                response.status_code,
                [status.HTTP_200_OK, status.HTTP_204_NO_CONTENT],
                f"Endpoint {endpoint} should return success when authenticated",
            )
