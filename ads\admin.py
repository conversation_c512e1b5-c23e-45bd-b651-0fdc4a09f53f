from django.contrib import admin
from .models import Advert, CountryOption
from unfold.admin import ModelAdmin

class AdvertAdmin(ModelAdmin):
    list_display = ["advertiser", "post", "ad_button", "paid", "created_at"]
    list_filter = ["paid", "created_at", "ad_button"]
    search_fields = ["advertiser__username", "post__content"]

class CountryOptionAdmin(ModelAdmin):
    list_display = ["code"]
    search_fields = ["code"]

admin.site.register(Advert, AdvertAdmin)
admin.site.register(CountryOption, CountryOptionAdmin)
