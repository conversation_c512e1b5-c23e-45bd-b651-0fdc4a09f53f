# Generated by Django 4.2.1 on 2024-04-06 11:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("post", "0014_alter_mention_user"),
        ("core", "0013_user_mfa_hash_alter_privacysettings_comments_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="LocationPreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("state", models.CharField(max_length=40)),
                (
                    "country",
                    django_countries.fields.CountryField(default="US", max_length=2),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Advert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("promotion_start_date", models.DateField()),
                ("promotion_end_date", models.DateField()),
                ("age_range_min", models.IntegerField(default=18)),
                ("age_range_max", models.IntegerField(default=65)),
                (
                    "ad_button",
                    models.CharField(
                        choices=[
                            ("send_dm", "Send Dm"),
                            ("view_profile", "Send Dm"),
                            ("connect", "Connect"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "advertiser",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="advert",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "audience_interests",
                    models.ManyToManyField(blank=True, to="core.interestchoice"),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sponsored_post",
                        to="post.post",
                    ),
                ),
                (
                    "preferred_location_reach",
                    models.ManyToManyField(to="ads.locationpreferences"),
                ),
            ],
        ),
    ]
