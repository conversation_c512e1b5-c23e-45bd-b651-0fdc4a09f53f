amqp==5.2.0
annotated-types==0.6.0
APScheduler==3.10.4
asgiref==3.6.0
async-timeout==4.0.3
attrs==23.1.0
autobahn==23.6.2
Automat==22.10.0
billiard==4.2.0
black==23.7.0
blis==0.7.11
CacheControl==0.13.1
cachetools==5.3.1
catalogue==2.0.10
celery==5.3.6
certifi==2023.5.7
cffi==1.15.1
channels==4.0.0
channels-redis==4.1.0
charset-normalizer==3.1.0
click==8.1.3
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
cloudinary==1.33.0
cloudpathlib==0.16.0
confection==0.1.4
constantly==23.10.4
cron-descriptor==1.4.5
cryptography==41.0.5
cymem==2.0.8
daphne==4.0.0
deptry==0.23.0
dj-database-url==2.0.0
Django==4.2.1
django-celery-beat==2.7.0
django-channels-jwt-auth-middleware==1.0.0
django-countries==7.5.1
django-filter==24.3
django-heroku==0.3.1
django-model-utils==4.3.1
django-notifications-hq==1.8.2
django-notifications-rest==0.0.1
django-phonenumber-field==7.1.0
django-ratelimit==4.1.0
django-redis==5.4.0
django-rest-framework==0.1.0
django-timezone-field==7.1
django-unfold==0.60.0
djangorestframework==3.14.0
djangorestframework-simplejwt==5.2.2
drf-spectacular==0.27.2
drf-writable-nested==0.7.0
drf-yasg==1.21.7
en-core-web-sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl
firebase-admin==6.2.0
google-api-core==2.11.1
google-api-python-client==2.99.0
google-auth==2.23.0
google-auth-httplib2==0.1.1
google-cloud-core==2.3.3
google-cloud-firestore==2.12.0
google-cloud-storage==2.10.0
google-crc32c==1.5.0
google-resumable-media==2.6.0
googleapis-common-protos==1.60.0
grpcio==1.58.0
grpcio-status==1.58.0
gunicorn==20.1.0
httplib2==0.22.0
hyperlink==21.0.0
idna==3.4
incremental==22.10.0
inflection==0.5.1
iniconfig==2.1.0
Jinja2==3.1.2
joblib==1.3.2
jsonfield==3.1.0
jsonschema==4.19.2
jsonschema-specifications==2023.11.1
kombu==5.3.4
langcodes==3.3.0
MarkupSafe==2.1.3
msgpack==1.0.5
murmurhash==1.0.10
mypy-extensions==1.0.0
numpy==1.26.2
packaging==25.0
pathspec==0.11.1
phonenumbers==8.13.14
Pillow==9.5.0
platformdirs==3.5.1
pluggy==1.5.0
preshed==3.0.9
prompt-toolkit==3.0.43
proto-plus==1.22.3
protobuf==4.24.3
psycopg2==2.9.6
pyasn1==0.5.0
pyasn1-modules==0.3.0
pycparser==2.21
pydantic==2.5.2
pydantic_core==2.14.5
PyJWT==2.7.0
pyOpenSSL==23.3.0
pyotp==2.9.0
pyparsing==3.1.1
pytest==8.3.5
python-crontab==3.2.0
python-dateutil==2.8.2
python-decouple==3.8
pytz==2023.3
PyYAML==6.0.1
redis==5.0.1
referencing==0.31.0
regex==2023.10.3
requests==2.31.0
requirements-parser==0.13.0
rpds-py==0.12.0
rsa==4.9
service-identity==23.1.0
six==1.16.0
smart-open==6.4.0
spacy==3.7.2
spacy-legacy==3.0.12
spacy-loggers==1.0.5
sqlparse==0.4.4
srsly==2.4.8
swapper==1.3.0
thinc==8.2.1
tqdm==4.66.1
Twisted==23.8.0
txaio==23.1.1
typer==0.9.0
typing_extensions==4.8.0
tzdata==2023.3
tzlocal==5.2
uritemplate==4.1.1
urllib3==1.26.16
vine==5.1.0
wasabi==1.1.2
wcwidth==0.2.12
weasel==0.3.4
whitenoise==6.6.0
zope.interface==6.1
