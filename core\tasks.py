from celery import shared_task
from django.conf import settings
import requests

product_url = f"{settings.WEBSITE_URL}"
product_name = "SmallClosedWorld"


# @classmethod
@shared_task
def send_signup_verification(verification_code, To):
    """
    Sends a verification email to a user who has signed up.

    Args:
        verification_link (str): The verification link that the user needs to click.
        To (str): The email address of the user.

    Returns:
        dict: The JSON response from the Postmark API, which includes information about the email sending status.
    """

    url = "https://api.postmarkapp.com/email/withTemplate"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "X-Postmark-Server-Token": "************************************",
    }

    data = {
        "From": "<EMAIL>",
        "To": To,
        "TemplateAlias": "sign-up-verification",
        "TemplateModel": {
            "product_url": product_url,
            "product_name": product_name,
            "verification_code": verification_code,
            "sender_name": "Onboarding",
            "company_name": product_name,
            "support_url": "mailto:<EMAIL>",
        },
    }

    response = requests.post(url, headers=headers, json=data)
    return response.json()


@shared_task
def send_reset_login(verification_code, To):
    """
    Verification Link Mail Function Using Postmark
    """
    url = "https://api.postmarkapp.com/email/withTemplate"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "X-Postmark-Server-Token": "************************************",
    }

    data = {
        "From": "<EMAIL>",
        "To": To,
        "TemplateAlias": "password-reset-1",
        "TemplateModel": {
            "product_url": product_url,
            "product_name": product_name,
            "verification_code": verification_code,
            "sender_name": "Onboarding",
            "company_name": product_name,
            "support_url": "mailto:<EMAIL>",
        },
    }

    response = requests.post(url, headers=headers, json=data)
    return response.json()


# @classmethod
@shared_task
def send_login_validation(verification_code, To):
    """
    Verification Link Mail Function Using Postmark
    """
    url = "https://api.postmarkapp.com/email/withTemplate"
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "X-Postmark-Server-Token": "************************************",
    }

    data = {
        "From": "<EMAIL>",
        "To": To,
        "TemplateAlias": "password-reset-1",
        "TemplateModel": {
            "product_url": product_url,
            "product_name": product_name,
            "verification_code": verification_code,
            "sender_name": "Support",
            "company_name": product_name,
            "support_url": "mailto:<EMAIL>",
        },
    }

    response = requests.post(url, headers=headers, json=data)
    return response.json()
