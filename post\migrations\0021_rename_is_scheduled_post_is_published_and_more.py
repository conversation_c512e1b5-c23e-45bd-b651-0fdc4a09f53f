# Generated by Django 4.2 on 2025-05-11 10:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("post", "0020_post_is_scheduled_post_scheduled_time"),
    ]

    operations = [
        migrations.RenameField(
            model_name="post",
            old_name="is_scheduled",
            new_name="is_published",
        ),
        migrations.RemoveField(
            model_name="post",
            name="scheduled_time",
        ),
        migrations.CreateModel(
            name="ScheduledPost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("scheduled_date", models.DateField()),
                ("scheduled_time", models.TimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "post",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="scheduled_post",
                        to="post.post",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
