# Generated by Django 4.2.1 on 2025-06-26 10:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("post", "0028_remove_post_is_boost"),
    ]

    operations = [
        migrations.AlterField(
            model_name="boost",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_boosts",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
