from django.test import TestCase
from core.models import User
from .models import Post, Comment, Mention
from django.urls import reverse
from notifications.models import Notification
from rest_framework.test import APIClient, APITestCase
from django.contrib.auth import get_user_model
from rest_framework import status


class PostTestCase(TestCase):
    """
    Testing the Post Views
    """

    def setUp(self):
        self.user_a = User.objects.create_user(
            email="<EMAIL>", password="testpassa", fcm_token=["token"]
        )

        self.user_b = User.objects.create_user(
            email="<EMAIL>", password="testpassb"
        )

        self.first_post = Post.objects.create(user=self.user_a, content="New Post")

        self.second_post = Post.objects.create(user=self.user_b, content="Post2")

    def get_client(self):
        client = APIClient()
        client.force_authenticate(self.user_a)
        return client

    def test_created_post(self):
        qs = Post.objects.all().count()
        self.assertEqual(qs, 2)

    def test_list_create_post_api_endpoint(self):
        client = self.get_client()
        create_response = client.post(
            reverse("post:posts-list"), {"content": "new post"}, format="json"
        )
        list_response = client.get(reverse("post:posts-list"))
        create_response_data = create_response.json()
        list_response_data = list_response.json()
        created_post_data = create_response_data.get("content")
        post_list_data = list_response_data
        count_list_data = len(post_list_data)
        self.assertEqual(created_post_data, "new post")
        self.assertEqual(count_list_data, 3)  # Including the two posts from setUp
        self.assertEqual(list_response.status_code, 200)

    def test_post_feed_endpoint(self):
        client = self.get_client()
        response = client.post(reverse("post:post-feed"))
        response_data = response.json()
        feed_item_count = len(response_data)
        self.assertEqual(feed_item_count, 1)

    def test_post_actions_endpoint(self):
        client = self.get_client()
        like_response = client.post(
            reverse("post:post-actions"),
            {"post_id": self.first_post.id, "action": "like"},
        )
        boost_response = client.post(
            reverse("post:post-actions"),
            {"post_id": self.first_post.id, "action": "boost"},
        )
        like_response_data = like_response.json()
        boost_response_data = boost_response.json()
        notification_objects = Notification.objects.all().count()
        self.assertEqual(notification_objects, 2)
        self.assertEqual(like_response_data["status"], "Liked")
        self.assertEqual(boost_response_data["status"], "Boosted")

    def test_boost_post_creates_boost_object_and_increases_count(self):
        """
        Test that boosting a post creates a Boost object and increases the boost count for the post.
        """
        client = self.get_client()
        # Boost the second post (created by user_b, so user_a can boost it)
        response = client.post(
            reverse("post:post-actions"),
            {"post_id": self.second_post.id, "action": "boost"},
        )
        print("BOOST RESPONSE:", response.status_code, response.json())  # Debug output
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json().get("status"), "Boosted")

        # Check that a Boost object exists for this post
        from django.contrib.contenttypes.models import ContentType
        from .models import Boost
        post_type = ContentType.objects.get_for_model(Post)
        boost_exists = Boost.objects.filter(content_type=post_type, object_id=self.second_post.id, user=self.user_a).exists()
        self.assertTrue(boost_exists)

        # Check that the boost count for the post is correct
        boost_count = Boost.objects.filter(content_type=post_type, object_id=self.second_post.id).count()
        self.assertEqual(boost_count, 1)

    def test_boost_count_increases_with_multiple_boosters(self):
        """
        Test that boost count increases when multiple users boost the same post.
        """
        client_a = self.get_client()
        client_b = APIClient()
        client_b.force_authenticate(self.user_b)

        # User A boosts
        client_a.post(
            reverse("post:post-actions"),
            {"post_id": self.first_post.id, "action": "boost"},
        )
        # User B boosts
        client_b.post(
            reverse("post:post-actions"),
            {"post_id": self.first_post.id, "action": "boost"},
        )
        from django.contrib.contenttypes.models import ContentType
        from .models import Boost
        post_type = ContentType.objects.get_for_model(Post)
        boost_count = Boost.objects.filter(content_type=post_type, object_id=self.first_post.id).count()
        self.assertEqual(boost_count, 2)

    def test_boost_requires_authentication(self):
        """
        Test that boosting a post without authentication fails.
        """
        url = reverse("post:post-actions")
        unauth_client = APIClient()  # Do not authenticate
        response = unauth_client.post(
            url,
            {"post_id": self.first_post.id, "action": "boost"},
        )
        self.assertIn(response.status_code, [401, 403])

    def test_get_mention_by_id(self):
        url = reverse("mention-get-mention-by-id", kwargs={"pk": self.mention_post.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.mention_post.id)
        self.assertEqual(response.data["user"]["username"], "user1")

    def test_get_mentions_by_user(self):
        url = reverse("mention-get-mentions-by-user", kwargs={"user_id": self.user1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data) >= 2)
        usernames = [m["user"]["username"] for m in response.data]
        self.assertTrue("user1" in usernames)

    def test_get_mentions_by_user_invalid_user(self):
        url = reverse("mention-get-mentions-by-user", kwargs={"user_id": 9999})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data["detail"], "User not found.")

    def test_mentions_by_post(self):
        url = reverse("mention-mentions-by-post", kwargs={"post_id": self.post.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(all(m["post"] == self.post.id for m in response.data))

    def test_mentions_by_comment(self):
        url = reverse(
            "mention-mentions-by-comment", kwargs={"comment_id": self.comment.id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(all(m["comment"] == self.comment.id for m in response.data))

    def test_mention_count_increases(self):
        """Test that creating a mention increases the mention count"""
        from analytics.models import MentionCount

        initial_count = MentionCount.objects.filter(user=self.user_a).count()

        # Create a new mention
        client = self.get_client()
        response = client.post(
            reverse("post:mentions-list"),
            {
                "user": self.user_a.id,
                "post": self.first_post.id,
            },
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Check that mention count increased
        new_count = MentionCount.objects.filter(user=self.user_a).count()
        self.assertGreater(new_count, initial_count)

        # Check the actual count value
        mention_count = MentionCount.objects.filter(user=self.user_a).first()
        self.assertIsNotNone(mention_count)
        self.assertGreaterEqual(mention_count.count, 1)

