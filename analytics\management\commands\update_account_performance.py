from typing import Any, Optional
from django.core.management.base import BaseCommand
from ...models import LikesCount
from core.models import User
from post.models import Post


class Command(BaseCommand):
    def handle(self, *args: Any, **options: Any) -> str | None:
        help = "Update Follower Count"
        users = User.objects.all()
        for user in users:
            count = {"boost_count": 0, "likes_count": 0, "comment_count": 0}
            posts = Post.objects.filter(user=user)
            for post in posts:
                count["boost_count"] += post.items_count["boost_count"]
                count["like_count"] += post.items_count["like_count"]
                count["comment_count"] += post.items_count["comment_count"]
            AccountPerformance.objects.create(
                user=user,
                like_count=count["like_count"],
                comment_count=["comment_count"],
                boost_count=["boost_count"],
            )
