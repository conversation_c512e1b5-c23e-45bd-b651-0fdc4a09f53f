from apscheduler.schedulers.blocking import BlockingScheduler
from django.core.management import call_command
import django
import os
import logging

logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "smallworld.settings")
django.setup()


def scheduled_job():
    logger.info("Running Scheduled Tasks...")
    try:
        call_command("generate_trending_hashtags")
        logger.info("generate_trending_hashtags complete")
    except Exception as e:
        logger.error(f"Error in generate_trending_hashtags: {e}")

    try:
        call_command("generate_trending_topics")
        logger.info("generate_trending_topics complete")
    except Exception as e:
        logger.error(f"Error in generate_trending_topics: {e}")

    try:
        call_command("update_analytics")
        logger.info("update_analytics complete")
    except Exception as e:
        logger.error(f"Error in update_analytics: {e}")
    logger.info("All Tasks Completed")
    # call_command("update_follower_count")  # Make sure command name matches


scheduler = BlockingScheduler()
scheduler.add_job(scheduled_job, "interval", hours=24)

if __name__ == "__main__":
    scheduler.start()


# from apscheduler.schedulers.blocking import BlockingScheduler
# from subprocess import call


# def scheduled_job():
#     call(["python", "manage.py", "generate_trending_hashtags"])
#     call(["python", "manage.py", "generate_trending_topics"])
#     call(["python", "manage.py", "Update_follower_count"])


# scheduler = BlockingScheduler()
# scheduler.add_job(scheduled_job, "interval", hours=24)

# if __name__ == "__main__":
#     scheduler.start()
