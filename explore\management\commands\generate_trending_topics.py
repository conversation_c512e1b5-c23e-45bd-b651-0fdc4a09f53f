from typing import Any, Optional
from django.core.management.base import BaseCommand
from post.models import Post
from explore.models import TrendingTopics
import spacy
from collections import Counter
from datetime import datetime, timedelta


class Command(BaseCommand):
    def handle(self, *args: Any, **options: Any) -> str | None:
        help = "Generate Trending Topics"
        one_day = datetime.now() - timedelta(days=1)
        recent_posts = Post.objects.filter(post_date__gte=one_day).values_list(
            "content", flat=True
        )
        if len(recent_posts) != 0:
            for trend in TrendingTopics.objects.all():
                trend.delete()

        all_contents = " ".join(recent_posts)

        nlp = spacy.load("en_core_web_sm")
        doc = nlp(all_contents)

        keywords = [token.text for token in doc if not token.is_stop and token.is_alpha]

        word_counts = Counter(keywords)
        top_words = word_counts.most_common(10)
        topics_to_create = [
            TrendingTopics(topic=word[0], occurrence=word[1]) for word in top_words
        ]

        # Bulk create the TrendingTopics objects
        TrendingTopics.objects.bulk_create(topics_to_create)
        self.stdout.write(
            f"{TrendingTopics.objects.count()} Topics Have Been Generated"
        )
