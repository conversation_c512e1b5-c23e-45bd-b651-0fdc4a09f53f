# Generated by Django 4.2.1 on 2024-02-14 13:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0011_rename_usersettings_notificationsettings"),
    ]

    operations = [
        migrations.CreateModel(
            name="PrivacySettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "comments",
                    models.CharField(
                        choices=[
                            ("everyone", "Everyone"),
                            ("no_one", "No One"),
                            ("following", "Following"),
                            ("followers_and_following", "Followers and Following"),
                        ],
                        max_length=256,
                    ),
                ),
                (
                    "tags",
                    models.CharField(
                        choices=[
                            ("everyone", "Everyone"),
                            ("no_one", "No One"),
                            ("following", "Following"),
                            ("followers_and_following", "Followers and Following"),
                        ],
                        max_length=256,
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
