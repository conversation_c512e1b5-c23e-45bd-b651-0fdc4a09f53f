from django.urls import path, include
from .views import (
    PostViewSet,
    post_action_view,
    comment_action_view,
    post_feed_view,
    SavedPostViewset,
    CollectionsViewset,
    ReportPostViewSet,
    CommentViewSet,
    MentionViewSet,
    BoostViewSet,
)

from rest_framework.routers import DefaultRouter


app_name = "post"
router = DefaultRouter()
router.register(r"post", PostViewSet, basename="posts")
router.register(r"comment", CommentViewSet, basename="comment")
router.register(r"saved", SavedPostViewset, basename="saved")
router.register(r"collections", CollectionsViewset, basename="collections")
router.register(r"report", ReportPostViewSet, basename="report")
router.register(r"mentions", MentionViewSet, basename="mentions")
router.register(r"boost", BoostViewSet, basename="boost")


urlpatterns = [
    path("post-actions/", post_action_view, name="post-actions"),
    path("comment-actions", comment_action_view, name="comment-actions"),
    path("post-feed/", post_feed_view, name="post-feed"),
    path("", include(router.urls))
]