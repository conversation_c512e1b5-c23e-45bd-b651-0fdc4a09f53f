from django.contrib import admin
from .models import TrendingHashtag, TrendingTopics
from unfold.admin import ModelAdmin

class TrendingHashtagAdmin(ModelAdmin):
    list_display = ["hashtag", "occurrence"]
    search_fields = ["hashtag"]

class TrendingTopicsAdmin(ModelAdmin):
    list_display = ["topic", "occurrence"]
    search_fields = ["topic"]

admin.site.register(TrendingHashtag, TrendingHashtagAdmin)
admin.site.register(TrendingTopics, TrendingTopicsAdmin)
