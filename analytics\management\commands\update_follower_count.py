from typing import Any, Optional
from django.core.management.base import BaseCommand
from ...models import FollowerCount, MentionCount
from core.models import User
from post.models import Mention


class Command(BaseCommand):
    def handle(self, *args: Any, **options: Any) -> str | None:
        help = "Update Follower Count"
        users = User.objects.all()
        for user in users:
            follower_count = user.followers.count()
            mention_count = user.mentions.count()
            FollowerCount.objects.create(user=user, count=follower_count)
            MentionCount.objects.create(user=user, count=mention_count)
