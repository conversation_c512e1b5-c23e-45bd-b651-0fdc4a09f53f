from django.contrib import admin
from chat.models import Message, Connection
from unfold.admin import ModelAdmin


class MessageAdmin(ModelAdmin):
    list_editable = ["is_read"]
    list_display = ["connection", "user", "text", "is_read", "created"]


class ConnectionAdmin(ModelAdmin):
    list_display = ["sender", "receiver", "accepted", "created", "blocked"]
    search_fields = ["sender__username", "receiver__username"]
    list_filter = ["accepted", "blocked", "created"]


admin.site.register(Message, MessageAdmin)
admin.site.register(Connection, ConnectionAdmin)
