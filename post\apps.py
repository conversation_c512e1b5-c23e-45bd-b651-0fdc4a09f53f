from django.apps import AppConfig


class PostConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "post"

    def ready(self):
        import post.signals  # noqa


# class PostConfig(AppConfig):
#     default_auto_field = 'django.db.models.BigAutoField'
#     name = 'post'

#     def ready(self):
#         # Start Celery worker and beat when Django starts
#         if os.environ.get('RUN_MAIN') == 'true':  # Prevent duplicate runs in development
#             self.start_celery_workers()

#     def start_celery_workers(self):
#         def run_worker():
#             subprocess.call([
#                 'celery', '-A', 'smallworld', 'worker',
#                 '--loglevel=info', '--without-gossip', '--without-mingle',
#                 '--without-heartbeat'
#             ])

#         def run_beat():
#             subprocess.call([
#                 'celery', '-A', 'smallworld', 'beat',
#                 '--loglevel=info', '--scheduler', 'celery.beat:PersistentScheduler'
#             ])

#         # Start worker thread
#         worker_thread = threading.Thread(target=run_worker)
#         worker_thread.daemon = True
#         worker_thread.start()

#         # Start beat thread
#         beat_thread = threading.Thread(target=run_beat)
#         beat_thread.daemon = True
#         beat_thread.start()


# class PostConfig(AppConfig):
#     default_auto_field = 'django.db.models.BigAutoField'
#     name = 'post'
#     _worker_started = False  # Class variable to track if worker was started

#     def ready(self):
#         # Start Celery worker and beat when Django starts
#         if os.environ.get('RUN_MAIN') == 'true' and not self._worker_started:
#             self._worker_started = True
#             self.start_celery_workers()


#     def start_celery_workers(self):
#         if not hasattr(self, '_celery_processes'):
#             self._celery_processes = []

#         def run_process(command):
#             proc = subprocess.Popen(
#                 command,
#                 stdout=subprocess.PIPE,
#                 stderr=subprocess.PIPE,
#                 text=True
#             )
#             self._celery_processes.append(proc)
#             return proc

#         # Start worker
#         worker_proc = run_process([
#             'celery', '-A', 'smallworld', 'worker',
#             '--concurrency=1',
#             '--loglevel=info'
#         ])

#         # Start beat with database scheduler
#         beat_proc = run_process([
#             'celery', '-A', 'smallworld', 'beat',
#             '--scheduler', 'django_celery_beat.schedulers:DatabaseScheduler',
#             '--loglevel=info'
#         ])

#         # Log output in background
#         threading.Thread(
#             target=self._log_output,
#             args=(worker_proc, beat_proc),
#             daemon=True
#         ).start()

#     def _log_output(self, *processes):
#         while True:
#             for proc in processes:
#                 if proc.stdout:
#                     for line in proc.stdout:
#                         print(f"[CELERY] {line.strip()}")
#             time.sleep(0.1)
