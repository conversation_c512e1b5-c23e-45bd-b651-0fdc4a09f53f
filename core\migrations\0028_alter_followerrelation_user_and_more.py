# Generated by Django 4.2.1 on 2025-06-15 18:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0027_alter_privacysettings_comments_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="followerrelation",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="follower_relations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="privacysettings",
            name="comments",
            field=models.CharField(
                choices=[
                    ("everyone", "Everyone"),
                    ("no_one", "Nobody"),
                    ("following", "Following"),
                    ("followers_and_following", "Followers and Following"),
                ],
                default="everyone",
                max_length=30,
            ),
        ),
        migrations.AlterField(
            model_name="privacysettings",
            name="tags",
            field=models.CharField(
                choices=[
                    ("everyone", "Everyone"),
                    ("no_one", "Nobody"),
                    ("following", "Following"),
                    ("followers_and_following", "Followers and Following"),
                ],
                default="everyone",
                max_length=30,
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="verification_status",
            field=models.CharField(
                choices=[("PND", "Pending"), ("VER", "Verified"), ("REJ", "Rejected")],
                default="PND",
                max_length=3,
            ),
        ),
    ]
