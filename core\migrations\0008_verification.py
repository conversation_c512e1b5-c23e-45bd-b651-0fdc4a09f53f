# Generated by Django 4.2.1 on 2024-01-31 10:05

import cloudinary.models
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0007_user_muted_users"),
    ]

    operations = [
        migrations.CreateModel(
            name="Verification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("legal_name", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "legal_business_name",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "govt_entity_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("account_username", models.CharField(max_length=100)),
                ("email", models.EmailField(max_length=254)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("personal", "Personal"),
                            ("politics", "Politics"),
                            ("entertainment", "Entertainment"),
                            ("government", "Government"),
                            ("sports", "Sports"),
                            ("news", "News/Media"),
                            ("other", "Other"),
                        ],
                        max_length=100,
                    ),
                ),
                ("dob", models.DateField(blank=True, null=True)),
                (
                    "government_id",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="government_id",
                    ),
                ),
                (
                    "business_reg",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="business_documents",
                    ),
                ),
                (
                    "address_proof",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="address_proof",
                    ),
                ),
                (
                    "photograph",
                    cloudinary.models.CloudinaryField(
                        blank=True, max_length=255, null=True, verbose_name="photograph"
                    ),
                ),
                (
                    "board_resolution",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="board_resolution",
                    ),
                ),
                ("tin", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "govt_request",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="govt_request",
                    ),
                ),
                (
                    "govt_entity_docs",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="govt_entity_docs",
                    ),
                ),
                (
                    "govt_legitimacy_doc",
                    cloudinary.models.CloudinaryField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="govt_legitimacy_doc",
                    ),
                ),
                ("request_reason", models.TextField()),
                ("additional_docs", models.TextField()),
            ],
        ),
    ]
