from django.db import models
from core.models import User
from post.models import Post


class Hub(models.Model):
    name = models.CharField(max_length=30)
    contributors = models.ManyToManyField(to=User, related_name="contributors")
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="created_hubs"
    )

    def post(self):
        posts = Post.objects.filter(user__in=self.contributors.all())
        return posts

    def __str__(self) -> str:
        return self.name
