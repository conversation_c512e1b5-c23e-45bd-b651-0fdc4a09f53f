import os
from celery import Celery
from celery.schedules import crontab


# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "smallworld.settings")

# Change "proj" to "smallworld" (matching your Django project)
app = Celery("smallworld")

# Load configuration from Django settings with `CELERY_` prefix
app.config_from_object("django.conf:settings", namespace="CELERY")

# Autodiscover tasks from all installed Django apps
app.autodiscover_tasks()


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f"Request: {self.request!r}")


app.conf.broker_connection_max_retries = 3

app.conf.broker_pool_limit = 10
app.conf.broker_connection_timeout = 30
app.conf.broker_connection_retry_on_startup = True
app.conf.broker_connection_max_retries = 5


app.conf.worker_concurrency = 1
app.conf.worker_prefetch_multiplier = 1  # Don't prefetch too many tasks

app.conf.worker_max_tasks_per_child = 10  # Restart workers after 10 tasks
app.conf.worker_max_memory_per_child = 200000  # 200MB (in KB)
app.conf.task_acks_late = True  # Prevents memory buildup
app.autodiscover_tasks()


# Register beat schedule here

# app.conf.beat_schedule = {
#     'publish-scheduled-posts': {
#         'task': 'post.tasks.publish_scheduled_posts',
#         'schedule': crontab(minute='*/5'),  # Every 5 minutes
#         'options': {'expires': 240}  # Expire if not run within 4 mins
#     }
# }
