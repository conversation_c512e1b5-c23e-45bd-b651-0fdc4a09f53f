# Generated by Django 4.2.1 on 2025-02-11 14:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("hub", "0002_remove_hub_contributors_hub_contributors"),
    ]

    operations = [
        migrations.AddField(
            model_name="hub",
            name="user",
            field=models.ForeignKey(
                default=12,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_hubs",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=False,
        ),
    ]
