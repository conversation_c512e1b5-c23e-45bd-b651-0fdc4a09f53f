from rest_framework import serializers
from .models import CallOut, CallOutAction, CallOutMedia, Response
from core.models import User


class FollowedUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "username", "profile_picture"]


class CallOutSerializer(serializers.ModelSerializer):
    total_agrees = serializers.IntegerField(source="total_agrees", read_only=True)
    total_disagrees = serializers.IntegerField(source="total_disagrees", read_only=True)

    followed_user = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.none()  # Will be set dynamically
    )

    class Meta:
        model = CallOut
        fields = [
            "id",
            "user",
            "followed_user",
            "message",
            "intent",
            "call_out_date",
            "total_agrees",
            "total_disagrees",
        ]
        read_only_fields = ["user", "call_out_date"]

    def __init__(self, *args, **kwargs):
        """Dynamically set queryset for followed_user field to suggest only followed users."""
        super().__init__(*args, **kwargs)
        request = self.context.get("request")
        if request and hasattr(request, "user"):
            self.fields["followed_user"].queryset = request.user.following.all()


class CallOutActionSerializer(serializers.ModelSerializer):
    class Meta:
        model = CallOutAction
        fields = ["id", "user", "call_out", "action", "created_at"]


class CallOutMediaSerializer(serializers.ModelSerializer):
    media = serializers.SerializerMethodField()

    def get_media(self, obj):
        if isinstance(obj.media, str):
            return obj.media.replace("http", "https")
        elif obj.media:
            return obj.media.url.replace("http", "https")
        return None

    class Meta:
        model = CallOutMedia
        fields = ["id", "media"]


class ResponseSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)  # Show username instead of ID

    class Meta:
        model = Response
        fields = ["id", "user", "call_out", "message", "response_time"]
        read_only_fields = [
            "user",
            "response_time",
        ]  # Ensure user and time are auto-set
