Frontend Messages to WebSocket for Testing
1. Fetch Message List

{
    "source": "message.list",
    "connectionId": 123,
    "page": 0
}
2. Fetch Friends List

{
    "source": "friends.list"
}
3. Mark Message as Read

{
    "source": "message.read",
    "username": "john_doe",
    "messageID": 456
}
4. User Typing Notification

{
    "source": "message.type",
    "username": "john_doe"
}
5. Send New Message

{
    "source": "message.send",
    "connectionId": 123,
    "message": "Hello, how are you?",
    "image": null,
    "video": null,
    "post": null
}
6. Accept Connection Request

{
    "source": "request.accept",
    "requestId": 789
}
7. Fetch Connection Requests

{
    "source": "request.list"
}
8. Search for a User

{
    "source": "search",
    "query": "john_doe"
}
9. Send Connection Request

{
    "source": "request.connect",
    "username": "john_doe"
}
