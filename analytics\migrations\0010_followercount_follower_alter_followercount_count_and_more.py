# Generated by Django 4.2 on 2025-05-01 14:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("analytics", "0009_accountsperformance_mentions_count_mentioncount"),
    ]

    operations = [
        migrations.AddField(
            model_name="followercount",
            name="follower",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="following_count",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="followercount",
            name="count",
            field=models.IntegerField(default=1, null=True),
        ),
        migrations.AlterField(
            model_name="followercount",
            name="user",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="follower_count",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="followercount",
            unique_together={("user", "follower")},
        ),
    ]
