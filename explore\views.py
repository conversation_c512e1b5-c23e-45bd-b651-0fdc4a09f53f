from .models import TrendingHashtag, TrendingTopics
from core.models import User
from core.serializers import UserSerializer
from django.shortcuts import render
from rest_framework import generics, status
from post.models import Post
from post.serializers import PostSerializer
from rest_framework.response import Response
from .serializers import SearchSerializer, HashTagSerializer, TrendingTopicSerializer
from django.db.models import Q
from rest_framework.permissions import IsAuthenticated
from core.models import User
from datetime import datetime, timedelta
from collections import Counter


class SearchView(generics.GenericAPIView):
    serializer_class = SearchSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request):
        blocked_users = request.user.blocked_users.all()
        muted_users = request.user.muted_users.all()
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_queryset = User.objects.prefetch_related().filter(
            ~Q(user__id__in=blocked_users),
            username__icontains=serializer.data["keyword"],
        )
        post_queryset = Post.objects.prefetch_related().filter(
            ~Q(user__id__in=blocked_users),
            ~Q(user__id__in=muted_users),
            content__icontains=serializer.data["keyword"],
        )
        user_serializer = UserSerializer(user_queryset, many=True)
        post_serializer = PostSerializer(post_queryset, many=True)
        return Response(
            data={"users": user_serializer.data, "posts": post_serializer.data}
        )


class TrendingHashtagsView(generics.ListAPIView):
    queryset = TrendingHashtag.objects.all()
    serializer_class = HashTagSerializer
    permission_classes = [IsAuthenticated]


class TrendingTopicsView(generics.ListAPIView):
    queryset = TrendingTopics.objects.all()
    serializer_class = TrendingTopicSerializer
    permission_classes = [IsAuthenticated]


class PeopleYouMayKnow(generics.ListAPIView):
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        following = user.following.all()
        blocked_users = user.blocked_users.all()
        suggested_users = (
            User.objects.filter(
                ~Q(user__id__in=blocked_users), Q(following__in=following)
            )
            .exclude(id=self.request.user.id)
            .distinct()
        )

        return suggested_users
