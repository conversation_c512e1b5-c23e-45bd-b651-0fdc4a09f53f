from .models import Advert, CountryOption, AdImpression
from core.serializers import Interest<PERSON><PERSON>iceSerializer, UserSerializer
from rest_framework.serializers import (
    ModelSerializer,
    PrimaryKeyRelatedField,
    CharField,
)
from django_countries.serializers import CountryFieldMixin
from core.models import InterestChoice
from post.serializers import PostSerializer
from post.models import Post


class CountryOptionSerializer(CountryFieldMixin, ModelSerializer):
    name = Char<PERSON>ield(source="code.name", read_only=True)

    class Meta:
        model = CountryOption
        fields = ["id", "code", "name"]


class AdSerializer(ModelSerializer):
    preferred_location_reach = CountryOptionSerializer(many=True, read_only=True)
    preferred_location_reach_ids = PrimaryKeyRelatedField(
        many=True,
        queryset=CountryOption.objects.all(),
        write_only=True,
        source="preferred_location_reach",
    )
    audience_interests = InterestChoiceSerializer(many=True, read_only=True)
    audience_interests_ids = PrimaryKeyRelatedField(
        many=True,
        queryset=InterestChoice.objects.all(),
        write_only=True,
        source="audience_interests",
    )
    advertiser = UserSerializer(read_only=True)
    post = PostSerializer(read_only=True)
    post_id = PrimaryKeyRelatedField(
        queryset=Post.objects.all(), write_only=True, source="post"
    )

    class Meta:
        model = Advert
        fields = [
            "id",
            "post",
            "post_id",
            "advertiser",
            "preferred_location_reach",
            "preferred_location_reach_ids",
            "promotion_start_date",
            "promotion_end_date",
            "audience_interests",
            "audience_interests_ids",
            "age_range_min",
            "age_range_max",
            "ad_button",
            "paid",
            "created_at",
        ]
        read_only_fields = ["paid", "created_at"]

    def create(self, validated_data):
        advert = super().create(validated_data)
        return advert

    def update(self, instance, validated_data):
        return super().update(instance, validated_data)


class AdImpressionSerializer(ModelSerializer):
    ad = AdSerializer(read_only=True)
    viewer = UserSerializer(read_only=True)

    class Meta:
        model = AdImpression
        fields = ["id", "ad", "viewer", "timestamp"]
        read_only_fields = ["timestamp"]
