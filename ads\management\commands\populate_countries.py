from django.core.management.base import BaseCommand
from django_countries import countries
from ads.models import CountryOption  # Adjust to your app's path


class Command(BaseCommand):
    help = "Populate the CountryOption table with all countries from django-countries"

    def handle(self, *args, **kwargs):
        count = 0
        for code, name in countries:
            obj, created = CountryOption.objects.get_or_create(code=code)
            if created:
                count += 1
        self.stdout.write(
            self.style.SUCCESS(f"✅ {count} countries added successfully.")
        )
