/* Django Daisy Custom Theme CSS */
/* File: static/css/admin-custom.css */

:root {
  /* Override DaisyUI warning color to red */
  --warning: 0 84% 50%; /* Red hue instead of yellow */
  
  /* Override DaisyUI primary/blue colors to #0054CB */
  --primary: 221 100% 40%; /* HSL equivalent of #0054CB */
  --primary-content: 0 0% 100%; /* White text on primary */
  
  /* Override other blue-related colors */
  --accent: 221 100% 40%; /* Use same blue for accent */
  --accent-content: 0 0% 100%;
  
  /* Info color (often blue) */
  --info: 221 100% 40%;
  --info-content: 0 0% 100%;
}

/* Warning messages to red */
.alert-warning {
  --alert-bg: #fef2f2; /* Red background */
  --alert-border: #fecaca; /* Red border */
  --alert-text: #991b1b; /* Dark red text */
  background-color: var(--alert-bg) !important;
  border-color: var(--alert-border) !important;
  color: var(--alert-text) !important;
}

/* Warning messages to red */
.alert-success {
  --alert-bg: green; /* Red background */
  --alert-border:green /* Red border */
  --alert-text: white; /* Dark red text */
    background-color: var(--alert-bg) !important;
    border-color: var(--alert-border) !important;
    color: var(--alert-text) !important;
    }









            /* Force red alert styling for warnings/errors */
        .alert-warning,
        .alert.alert-warning,
        .messagelist .warning,
        .messages .warning {
            background-color: #dc2626 !important;
            color: white !important;
            border-color: #dc2626 !important;
            border-left: 4px solid #b91c1c !important;
        }
        /* Success (green) styling for success messages */
        .alert-success,
        .alert.alert-success,
        .messagelist .success,
        .messages .success {
            background-color: #16a34a !important;
            color: white !important;
            border-color: #16a34a !important;
            border-left: 4px solid #166534 !important;
        }




        
/* Primary buttons and elements */
.btn-primary,
.btn.btn-primary {
  background-color: #0054CB !important;
  border-color: #0054CB !important;
  color: white !important;
}

.btn-primary:hover,
.btn.btn-primary:hover {
  background-color: #003a8f !important;
  border-color: #003a8f !important;
}

/* Links and text */
.link-primary,
a.link-primary {
  color: #0054CB !important;
}

    .text-primary {
  color: #0054CB !important;
}

/* Backgrounds */
.bg-primary {
  background-color: #0054CB !important;
}

/* Borders */
.border-primary {
  border-color: #0054CB !important;
}

/* Info elements (often blue) */
.alert-info {
  background-color: #eff6ff !important;
  border-color: #93c5fd !important;
  color: #1e40af !important;
}

.btn-info {
  background-color: #0054CB !important;
  border-color: #0054CB !important;
  color: white !important;
}

/* Django admin specific overrides */
.submit-row input[type="submit"],
.button,
input[type="submit"],
input[type="button"] {
  background-color: #0054CB !important;
  border-color: #0054CB !important;
}

.submit-row input[type="submit"]:hover,
.button:hover,
input[type="submit"]:hover,
input[type="button"]:hover {
  background-color: #003a8f !important;
}

/* Navigation and sidebar */
.sidebar a.active,
.nav-item.active {
  background-color: #0054CB !important;
}

/* Progress bars */
.progress-primary {
  background-color: #0054CB !important;
}

/* Checkboxes and form controls */
.checkbox-primary:checked,
.radio-primary:checked {
  background-color: #0054CB !important;
  border-color: #0054CB !important;
}

/* Tabs */
.tab-active,
.tabs-boxed .tab-active {
  background-color: #0054CB !important;
  color: white !important;
}

/* Badges */
.badge-primary {
  background-color: #0054CB !important;
  color: white !important;
}

/* Loading indicators */
.loading {
  border-top-color: #0054CB !important;
}

/* Table styles */
#result_list {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

#result_list th {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
}

#result_list td {
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

#result_list tr:hover {
    background-color: #f9fafb;
}

#result_list .row1 {
    background-color: white;
}

#result_list .row2 {
    background-color: #f8f9fa;
}

/* Action form styles */
.action-select {
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-right: 0.5rem;
}

/* Checkbox styles in table */
#result_list .action-checkbox {
    width: 1rem;
    height: 1rem;
    margin: 0;
    vertical-align: middle;
}