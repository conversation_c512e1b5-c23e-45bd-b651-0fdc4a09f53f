from rest_framework import serializers
from .models import TrendingHashtag, TrendingTopics


class SearchSerializer(serializers.Serializer):
    keyword = serializers.CharField(max_length=500)

    class Meta:
        fields = ["keyword"]


class HashTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrendingHashtag
        fields = ["id", "hashtag", "occurrence"]


class TrendingTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrendingTopics
        fields = ["id", "topic", "occurrence"]
