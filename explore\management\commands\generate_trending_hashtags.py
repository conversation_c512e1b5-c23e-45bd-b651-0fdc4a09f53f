from typing import Any, Optional
from django.core.management.base import BaseCommand

from post.models import Post
from explore.models import TrendingHashtag
from collections import Counter
from datetime import datetime, timedelta


class Command(BaseCommand):
    def handle(self, *args: Any, **options: Any) -> str | None:
        help = "This is to generate trending hashtags"

        one_day_ago = datetime.now().replace(
            minute=0, second=0, microsecond=0
        ) - timedelta(hours=24)
        posts_list = Post.objects.filter(post_date__gte=one_day_ago).values_list(
            "content", flat=True
        )
        if len(posts_list) != 0:
            for trend in TrendingHashtag.objects.all():
                trend.delete()
        trends = []
        for post in posts_list:
            hashtags = self.extract_hashtags(post, trends)

        trends_counter = Counter(trends).most_common(10)
        for trend in trends_counter:
            TrendingHashtag.objects.create(hashtag=trend[0], occurrence=trend[1])
        self.stdout.write(
            f"{TrendingHashtag.objects.count()} Trending Hashtags Generated"
        )

    @staticmethod
    def extract_hashtags(text, trends):
        # splitting the text into words
        if text:
            for word in text.split():
                # checking the first character of every word
                if word[0] == "#":
                    # adding the word to the hashtag_list
                    trends.append(word[1:])

            return trends
        pass
