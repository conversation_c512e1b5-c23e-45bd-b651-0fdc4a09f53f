from django.db import models
from core.models import User
from cloudinary.models import CloudinaryField

# Create your models here.


class Callout_Choice(models.Model):
    title = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title


class CallOutAction(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    call_out = models.ForeignKey(
        "CallOut", on_delete=models.CASCADE, related_name="actions"
    )
    action = models.ForeignKey(
        Callout_Choice, on_delete=models.CASCADE, null=True, blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = (
            "user",
            "call_out",
        )  # Ensures a user can only agree or disagree once

    def __str__(self):
        return f"{self.user} {self.action}d {self.call_out}"


class Intent(models.Model):
    title = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title


class CallOut(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="created_callouts"
    )  # The creator
    followed_user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="received_callouts"
    )  # The person being called out
    intent = models.ForeignKey(Intent, on_delete=models.SET_NULL, null=True, blank=True)
    message = models.TextField(null=True, blank=True)
    call_out_date = models.DateTimeField(auto_now_add=True)

    def total_agrees(self):
        return self.actions.filter(action="agree").count()

    def total_disagrees(self):
        return self.actions.filter(action="disagree").count()

    def __str__(self):
        return f"CallOut by {self.user} to {self.followed_user}"


class CallOutMedia(models.Model):
    call_out = models.ForeignKey(
        CallOut, on_delete=models.CASCADE, related_name="images"
    )
    media = CloudinaryField("media", blank=True, null=True, resource_type="auto")

    def __str__(self):
        str(self.User) + "Media"


class Response(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="response")
    call_out = models.ForeignKey(
        CallOut, on_delete=models.CASCADE, related_name="response"
    )
    message = models.TextField(null=True, blank=True)
    response_time = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.call_out
