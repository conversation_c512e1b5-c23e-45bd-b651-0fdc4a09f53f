from typing import Iterable, Optional
from django.db import models
from core.models import User
import secrets


class Payment(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="Payments")
    amount = models.PositiveIntegerField()
    ref = models.CharField(max_length=200)
    verified = models.BooleanField
    date_created = models.DateField(auto_now_add=True)

    class Meta:
        ordering = ("-date_created",)

    def __str__(self):
        return f"Payment: {self.amount}"

    def save(self, *arge, **kwargs) -> None:
        while not self.ref:
            ref = secrets.token_urlsafe(50)
            objects_with_similar_reference = Payment.objects.filter(ref=ref)
            if not objects_with_similar_reference:
                self.ref = ref
            super().save(*args, **kwargs)

    def amount_value(self) -> int:
        return self.amount * 100
