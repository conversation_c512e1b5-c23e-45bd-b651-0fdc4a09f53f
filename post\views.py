from rest_framework import status
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes, action
from django.utils import timezone
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .serializers import (
    PostSerializer,
    PostActionSerializer,
    CommentActionSerializer,
    PostCreateSerializer,
    CollectionSerializer,
    SavedPostSerializer,
    ReportPostSerializer,
    CommentSerializer,
    MentionSerializer,
    BoostSerializer,
    UnboostSerializer
)
from core.models import User, PrivacySettings, NotificationSettings, Interest
from .models import (
    Post,
    PostImage,
    Collection,
    SavedPost,
    ReportPost,
    Comment,
    Mention,
    UninterestedPost,
    Boost
)
from ads.models import Advert
from ads.serializers import AdSerializer
from rest_framework import status, viewsets
from rest_framework.views import APIView
from django.db.models import Q
from notifications.signals import notify
from core.FCMManager import sendpush
from datetime import datetime
from django.utils.dateparse import parse_datetime
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.contrib.contenttypes.models import ContentType
from analytics.models import MentionCount
from django.utils import timezone


class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all()
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == "create":
            return PostCreateSerializer
        return PostSerializer

    @swagger_auto_schema(
        operation_summary="Create a new post (immediate or scheduled)",
        operation_description=""" 
        - If both `scheduled_date` and `scheduled_time` are provided, the post will be scheduled.
        - If none are provided, it is published immediately.
        - Replies can also be included in the payload under `replies` (optional).
        - Media files can be uploaded via multipart/form-data.
        """,
        request_body=PostCreateSerializer,
        responses={
            201: openapi.Response("Post Created", PostSerializer),
            400: "Invalid data",
            401: "Unauthorized",
        },
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Retrieve scheduled date and time
        scheduled_date = serializer.validated_data.get("scheduled_date")
        scheduled_time = serializer.validated_data.get("scheduled_time")

        # Determine if the post should be scheduled or published immediately
        is_published = True
        scheduled_datetime = None

        if scheduled_date and scheduled_time:
            try:
                naive_dt = datetime.combine(scheduled_date, scheduled_time)
                scheduled_datetime = timezone.make_aware(naive_dt)

                # If the scheduled datetime is in the past, publish immediately
                if scheduled_datetime <= timezone.now():
                    is_published = True
                    scheduled_datetime = (
                        None  # Set to None as it's published immediately
                    )
            except Exception as e:
                return Response(
                    {"error": f"Invalid scheduled datetime: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        else:
            is_published = True

        # Create the post instance
        post = serializer.save(
            user=request.user,
            is_published=is_published,
            scheduled_date=scheduled_date,
            scheduled_time=scheduled_time,
        )

        # Handle media files
        media_files = request.FILES.getlist("media")
        for media_file in media_files:
            PostImage.objects.create(post=post, media=media_file)

        # Handle replies if any
        replies_data = request.data.get("replies", [])
        if isinstance(replies_data, list):
            for reply_data in replies_data:
                reply_serializer = PostCreateSerializer(data=reply_data)
                if reply_serializer.is_valid():
                    reply = reply_serializer.save(
                        parent=post,
                        user=request.user,
                        is_published=True,  # Replies are immediately published
                    )
                    # Handle media for each reply
                    reply_media_files = reply_data.get("media", [])
                    for media in reply_media_files:
                        PostImage.objects.create(post=reply, media=media)

        return Response(PostSerializer(post).data, status=status.HTTP_201_CREATED)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        username = request.query_params.get("username")

        if username:
            # Show only published posts for other users
            queryset = queryset.filter(user__username=username, is_published=True)
            if not queryset.exists():
                return Response(
                    {"error": f"User '{username}' not found or no published posts"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        else:
            # Show all posts (including unpublished) for authenticated user
            queryset = queryset.filter(user=request.user)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.user != request.user:
            return Response(
                {"error": "You can only delete your own posts"},
                status=status.HTTP_403_FORBIDDEN,
            )
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)


class CommentViewSet(viewsets.ModelViewSet):
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=["get"])
    def post_comments(self, request):
        post_id = request.GET.get("post_id")
        if not post_id:
            return Response(
                {"error": "Post ID is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            comments = Comment.objects.filter(post_id=post_id).select_related("user")
            serializer = CommentSerializer(comments, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BoostViewSet(viewsets.ModelViewSet):
    queryset = Boost.objects.all()
    serializer_class = BoostSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        user = self.request.user
        boost = serializer.save(user=user)

        content_object = boost.content_object
        target_user = getattr(content_object, 'user', None)
        if not target_user or target_user == user:
            return  # Don’t notify self or invalid target

        notification_status = {}

        # Check user notification settings
        settings, _ = NotificationSettings.objects.get_or_create(user=target_user)
        if settings.enable_boost_push_notifications:
            # Save notification
            notify.send(
                sender=user,
                recipient=target_user,
                verb=f"Boosted your {boost.content_type.model}",
                action_object=content_object,
            )

            # Send push notification
            send_notification = sendpush(
                title="New Boost",
                msg=f"{user.username} boosted your {boost.content_type.model}",
                registration_token=target_user.fcm_token,
                dataObject={
                    "user": user.username,
                    "type": boost.content_type.model,
                    "id": boost.object_id,
                },
            )

            if send_notification and hasattr(send_notification, "responses"):
                notification_status[f"{user} boosted"] = [
                    i.exception for i in send_notification.responses
                ]
            else:
                notification_status[f"{user} boosted"] = "Notification not sent or no response"
    def get_queryset(self):
        return Boost.objects.filter(user=self.request.user)
    
    @action(detail=False, methods=['post'], url_path='unboost')
    def unboost(self, request):
        serializer = UnboostSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        content_type = serializer.validated_data['content_type']
        object_id = serializer.validated_data['object_id']

        Boost.objects.filter(
            user=request.user,
            content_type=content_type,
            object_id=object_id
        ).delete()

        return Response(status=204)



class MentionViewSet(viewsets.ModelViewSet):
    serializer_class = MentionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Mention.objects.all()
        
    def perform_create(self, serializer):
        # Save the mention
        mention = serializer.save()
        
      
        
        # Get or create today's mention count for the user
        today = timezone.now().date()
        mention_count, created = MentionCount.objects.get_or_create(
            user=mention.user,
            timestamp__date=today,
            defaults={'timestamp': timezone.now(), 'count': 0}
        )
        
        # Increment the count
        mention_count.count += 1
        mention_count.save()

    @action(detail=False, methods=["get"], url_path="user/(?P<user_id>[^/.]+)")
    def get_mentions_by_user(self, request, user_id=None):
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response(
                {"detail": "User not found."}, status=status.HTTP_404_NOT_FOUND
            )

        mentions = (
            Mention.objects.filter(user=user)
            .select_related("user", "post", "comment")
            .order_by("-created_at")
        )
        serializer = MentionSerializer(mentions, many=True)
        return Response(serializer.data)

    @action(
        detail=False,
        methods=["get"],
        url_path="post/(?P<post_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def mentions_by_post(self, request, post_id):
        mentions = Mention.objects.filter(post_id=post_id)
        serializer = self.get_serializer(mentions, many=True)
        return Response(serializer.data)

    @action(
        detail=False,
        methods=["get"],
        url_path="comment/(?P<comment_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def mentions_by_comment(self, request, comment_id):
        mentions = Mention.objects.filter(comment_id=comment_id)
        serializer = self.get_serializer(mentions, many=True)
        return Response(serializer.data)

    @action(
        detail=True,
        methods=["put"],
        url_path="read-by-id",
        permission_classes=[IsAuthenticated],
    )
    def mark_single_read(self, request, pk=None):
        mention = get_object_or_404(Mention, pk=pk)

        is_read = request.data.get("is_read")
        if is_read is not None:
            mention.is_read = is_read
            mention.save(update_fields=["is_read"])
        else:
            mention.save(update_fields=["is_read"])

        serializer = self.get_serializer(mention)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["put"],
        url_path="mark-all-read-by-user",
        permission_classes=[IsAuthenticated],
    )
    def mark_all_read_by_user(self, request):
        is_read = request.data.get("is_read")
        mentions = Mention.objects.filter(user=request.user)

        if is_read is not None:
            mentions.update(is_read=is_read)
        else:
            mentions.update(is_read=is_read)

        serializer = self.get_serializer(mentions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ReportPostViewSet(viewsets.ModelViewSet):
    serializer_class = ReportPostSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def get_queryset(self):
        return (
            ReportPost.objects.filter(user=self.request.user)
            if self.request.user.is_authenticated
            else ReportPost.objects.none()
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def post_action_view(request, *args, **kwargs):
    """
    id is required.
    Action Options Are: like, unlike, boost
    """
    user = request.user
    serializer = PostActionSerializer(data=request.POST)
    notification_status = {}
    notification_settings_get_and_check = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    notification_settings = notification_settings_get_and_check[0]
    if serializer.is_valid(raise_exception=True):
        data = serializer.validated_data
        post_id = data.get("post_id")
        action = data.get("action")
        content = data.get("content")
        queryset = Post.objects.filter(id=post_id)
        if not queryset.exists():
            return Response({}, status=404)
        obj = queryset.first()
        if action == "like":
            obj.likes.add(user)
            if notification_settings.likes_push_notifications:
                # Save to Notifications model
                notify.send(
                    sender=user,
                    recipient=obj.user,
                    verb="Liked your post",
                    action_object=obj,
                )
                # Send Firebase Notification
                send_notification = sendpush(
                    title="New Like",
                    msg=f"{user} liked your post",
                    registration_token=user.fcm_token,
                    dataObject={"user": f"{user}"},
                )
                if send_notification and hasattr(send_notification, "responses"):
                    notification_status[f"{user} liked"] = (
                        "\n" + f"{[i.exception for i in send_notification.responses]}"
                    )

                else:
                    notification_status[
                        f"{user} liked"
                    ] = "Notification not sent or no response"
            return Response(
                {
                    "status": "Liked",
                    "notification_status": notification_status,
                    "code": 200,
                },
                status.HTTP_200_OK,
            )
        elif action == "unlike":
            obj.likes.remove(user)
            return Response({"status": "Like Removed", "code": 200}, status.HTTP_200_OK)

        elif action == "boost":
            # Use the new Boost model instead of creating boost posts
            from django.contrib.contenttypes.models import ContentType

            # Check if user is trying to boost their own post
            if obj.user == user:
                return Response(
                    {"message": "You cannot boost your own post"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if user already boosted this post
            post_content_type = ContentType.objects.get_for_model(Post)
            already_boosted = Boost.objects.filter(
                user=user,
                content_type=post_content_type,
                object_id=obj.id
            ).exists()

            if already_boosted:
                return Response(
                    {"message": "You have already boosted this post"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create the boost
            boost = Boost.objects.create(
                user=user,
                content_type=post_content_type,
                object_id=obj.id
            )

            # Send notification to the post owner
            if obj.user != user:
                if notification_settings.enable_boost_push_notifications:
                    send_notification = notify.send(
                        user,
                        recipient=obj.user,
                        verb="boosted your post",
                        action_object=obj,
                        target=obj,
                        description=f"{user.username} boosted your post",
                    )

                    if send_notification and hasattr(send_notification, "responses"):
                        notification_status[f"{user} boosted"] = [
                            i.exception for i in send_notification.responses
                        ]
                    else:
                        notification_status[f"{user} boosted"] = "Notification not sent or no response"

            return Response(
                {
                    "notification_status": notification_status,
                    "status": "Boosted",
                    "code": 200,
                },
                status=200,
            )
        elif action == "uninterested":
            UninterestedPost.objects.create(user=user, post=obj)
            return Response({"status": "Uninterested Post Excludued", "code": 200})

        return Response({}, 200)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def comment_action_view(request, *args, **kwargs):
    """
    id is required.
    Action Options Are: like, unlike, boost
    """
    user = request.user
    serializer = CommentActionSerializer(data=request.POST)
    notification_status = {}
    notification_settings_get_and_check = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    notification_settings = notification_settings_get_and_check[0]
    if serializer.is_valid(raise_exception=True):
        data = serializer.validated_data
        comment_id = data.get("comment_id")
        action = data.get("action")
        content = data.get("content")
        obj = get_object_or_404(Comment, id=comment_id)
        if action == "like":
            obj.likes.add(user)
            if notification_settings.likes_push_notifications:
                # Save to Notifications model
                notify.send(
                    sender=user,
                    recipient=obj.user,
                    verb="Liked your comment",
                    action_object=obj,
                )
                # Send Firebase Notification
                send_notification = sendpush(
                    title="New Like",
                    msg=f"{user} liked your comment",
                    registration_token=user.fcm_token,
                    dataObject={"user": f"{user}", "comment": f"{obj}"},
                )
                if send_notification and hasattr(send_notification, "responses"):
                    notification_status[f"{user} liked"] = (
                        "\n" + f"{[i.exception for i in send_notification.responses]}"
                    )

                else:
                    notification_status[
                        f"{user} liked"
                    ] = "Notification not sent or no response"
            return Response(
                {
                    "status": "Liked",
                    "notification_status": notification_status,
                    "code": 200,
                },
                status.HTTP_200_OK,
            )
        elif action == "unlike":
            obj.likes.remove(user)
            return Response({"status": "Like Removed", "code": 200}, status.HTTP_200_OK)

        # elif action == "boost":
        #     # CHANGES:
        #     # 1. Removed unnecessary if not new_post.content check since we always want to serialize
        #     # 2. Moved serializer call outside notification block
        #     # 3. Added proper notification_status dict initialization
        #     # 4. Improved notification data structure
        #     # 5. Added error handling for notification responses
        #     # 6. Cleaned up formatting and added logical grouping

        #     # Create new post as a boost of the original
        #     #create a new post as boost
        #     new_post = Post.objects.create(user=user, parent=obj, content=content)

        #     # Always serialize the new post
        #     serializer = PostSerializer(new_post)

        #     # Initialize notification status
        #     notification_status = {}

        #     # Handle notifications if enabled
        #     if notification_settings.enable_boost_push_notifications:
        #         # Save notification to database
        #         notify.send(
        #             sender=user,
        #             recipient=obj.user,
        #             verb="Boosted your comment",
        #             action_object=obj,
        #         )

        #         # Prepare notification data with proper string conversion
        #         notification_data = {"user": str(new_post), "post": str(obj)}

        #         # Send push notification
        #         send_notification = sendpush(
        #             title="Comment Boosted",
        #             msg=f"{user} Boosted Your comment",
        #             registration_token=user.fcm_token,
        #             dataObject=notification_data,
        #         )

        #         # Track notification errors if any responses exist
        #         if hasattr(send_notification, "responses"):
        #             notification_status = {
        #                 "errors": [
        #                     str(resp.exception) for resp in send_notification.responses
        #                 ]
        #             }

        #     return Response(
        #         {
        #             "data": serializer.data,
        #             "status": "Boosted",
        #             "notification_status": notification_status,
        #             "code": 200,
        #         },
        #         status=status.HTTP_200_OK,
        #     )
        return Response({}, status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def post_feed_view(request):
    user = request.user
    interests = Interest.objects.values_list("user", flat=True)

    if user.id not in interests:
        return Response({"User Has No Interest"}, status.HTTP_409_CONFLICT)

    page = int(request.GET.get("page", 1))
    page_size = 10
    start = (page - 1) * page_size
    end = start + page_size

    blocked_users = user.blocked_users.values_list("id", flat=True)
    muted_users = user.muted_users.values_list("id", flat=True)
    reported_posts = user.reported_post.values_list("post__id", flat=True)
    users_to_exclude = list(blocked_users) + list(muted_users)

    post_type = ContentType.objects.get_for_model(Post)

    # Boosts (excluding blocked/muted/reported)
    boost_qs = Boost.objects.filter(
        content_type=post_type
    ).exclude(
        user__id__in=users_to_exclude
    )

    boosted_post_ids = boost_qs.values_list("object_id", flat=True)
    boosted_posts = Post.objects.filter(
        id__in=boosted_post_ids
    ).exclude(
        Q(user__id__in=users_to_exclude) | Q(id__in=reported_posts)
    )

    # Attach boosting user info
    boost_map = {b.object_id: b.user for b in boost_qs.select_related('user')}

    for post in boosted_posts:
        booster = boost_map.get(post.id)
        if booster:
            post._boosted_by_user = booster

    # Original posts
    normal_posts = Post.objects.exclude(
        Q(user__id__in=users_to_exclude) | Q(id__in=reported_posts)
    ).exclude(id__in=boosted_post_ids)

    # Combine & sort (boosted first or all mixed by date)
    all_posts = list(boosted_posts) + list(normal_posts)
    all_posts.sort(key=lambda p: p.post_date, reverse=True)

    # Pagination
    paginated_posts = all_posts[start:end]
    serializer = PostSerializer(paginated_posts, many=True)

    return Response({
        "results": serializer.data,
        "page": page,
        "has_next": end < len(all_posts),
    }, status=status.HTTP_200_OK)


class CollectionsViewset(viewsets.ModelViewSet):
    serializer_class = CollectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if getattr(
            self, "swagger_fake_view", False
        ):  # drf-yasg schema generation check
            return Collection.objects.none()
        return Collection.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
        return super().perform_create(serializer)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def collection_posts(self, request):
        collection_id = request.GET.get("id")
        if not collection_id:
            return Response(
                {"error": "Collection ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            saved_posts = SavedPost.objects.filter(
                collection_id=collection_id, collection__user=self.request.user
            ).select_related("post")
            print(self.request.user)
            posts = [saved_post.post for saved_post in saved_posts]
            serialized_posts = PostSerializer(posts, many=True).data
            return Response(serialized_posts, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SavedPostViewset(viewsets.ModelViewSet):
    serializer_class = SavedPostSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if getattr(
            self, "swagger_fake_view", False
        ):  # drf-yasg schema generation check
            return SavedPost.objects.none()
        return SavedPost.objects.filter(collection__user=self.request.user)
