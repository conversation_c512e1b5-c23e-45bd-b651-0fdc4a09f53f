from rest_framework import status
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes, action
from django.utils import timezone
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from core.serializers import (
    UserSerializer
)
from .serializers import (
    PostSerializer,
    PostActionSerializer,
    CommentActionSerializer,
    PostCreateSerializer,
    CollectionSerializer,
    SavedPostSerializer,
    ReportPostSerializer,
    CommentSerializer,
    MentionSerializer,
    BoostSerializer,
    UnboostSerializer
)
from core.models import User, PrivacySettings, NotificationSettings, Interest
from .models import (
    Post,
    PostImage,
    Collection,
    SavedPost,
    ReportPost,
    Comment,
    Mention,
    UninterestedPost,
    Boost
)
from ads.models import Advert
from ads.serializers import AdSerializer
from rest_framework import status, viewsets
from rest_framework.views import APIView
from django.db.models import Q
from notifications.signals import notify
from core.FCMManager import sendpush
from datetime import datetime
from django.utils.dateparse import parse_datetime
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.contrib.contenttypes.models import ContentType
from collections import defaultdict
from django.utils.dateparse import parse_datetime

def get_sort_date(item):
    if "boost_info" in item:
        return item["boost_info"]["boost_date"]
    post_date_str = item["original_post"].get("post_date")
    return parse_datetime(post_date_str) if post_date_str else datetime.min


class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all()
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == "create":
            return PostCreateSerializer
        return PostSerializer

    @swagger_auto_schema(
        operation_summary="Create a new post (immediate or scheduled)",
        operation_description=""" 
        - If both `scheduled_date` and `scheduled_time` are provided, the post will be scheduled.
        - If none are provided, it is published immediately.
        - Replies can also be included in the payload under `replies` (optional).
        - Media files can be uploaded via multipart/form-data.
        """,
        request_body=PostCreateSerializer,
        responses={
            201: openapi.Response("Post Created", PostSerializer),
            400: "Invalid data",
            401: "Unauthorized",
        },
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Retrieve scheduled date and time
        scheduled_date = serializer.validated_data.get("scheduled_date")
        scheduled_time = serializer.validated_data.get("scheduled_time")

        # Determine if the post should be scheduled or published immediately
        is_published = True
        scheduled_datetime = None

        if scheduled_date and scheduled_time:
            try:
                naive_dt = datetime.combine(scheduled_date, scheduled_time)
                scheduled_datetime = timezone.make_aware(naive_dt)

                # If the scheduled datetime is in the past, publish immediately
                if scheduled_datetime <= timezone.now():
                    is_published = True
                    scheduled_datetime = (
                        None  # Set to None as it's published immediately
                    )
            except Exception as e:
                return Response(
                    {"error": f"Invalid scheduled datetime: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        else:
            is_published = True

        # Create the post instance
        post = serializer.save(
            user=request.user,
            is_published=is_published,
            scheduled_date=scheduled_date,
            scheduled_time=scheduled_time,
        )

        # Handle media files
        media_files = request.FILES.getlist("media")
        for media_file in media_files:
            PostImage.objects.create(post=post, media=media_file)

        # Handle replies if any
        replies_data = request.data.get("replies", [])
        if isinstance(replies_data, list):
            for reply_data in replies_data:
                reply_serializer = PostCreateSerializer(data=reply_data)
                if reply_serializer.is_valid():
                    reply = reply_serializer.save(
                        parent=post,
                        user=request.user,
                        is_published=True,  # Replies are immediately published
                    )
                    # Handle media for each reply
                    reply_media_files = reply_data.get("media", [])
                    for media in reply_media_files:
                        PostImage.objects.create(post=reply, media=media)

        return Response(PostSerializer(post).data, status=status.HTTP_201_CREATED)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        username = request.query_params.get("username")

        if username:
            # Show only published posts for other users
            queryset = queryset.filter(user__username=username, is_published=True)
            if not queryset.exists():
                return Response(
                    {"error": f"User '{username}' not found or no published posts"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        else:
            # Show all posts (including unpublished) for authenticated user
            queryset = queryset.filter(user=request.user)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.user != request.user:
            return Response(
                {"error": "You can only delete your own posts"},
                status=status.HTTP_403_FORBIDDEN,
            )
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)


class CommentViewSet(viewsets.ModelViewSet):
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=["get"])
    def post_comments(self, request):
        post_id = request.GET.get("post_id")
        if not post_id:
            return Response(
                {"error": "Post ID is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            comments = Comment.objects.filter(post_id=post_id).select_related("user")
            serializer = CommentSerializer(comments, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BoostViewSet(viewsets.ModelViewSet):
    queryset = Boost.objects.all()
    serializer_class = BoostSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        user = self.request.user
        boost = serializer.save(user=user)

        content_object = boost.content_object
        target_user = getattr(content_object, 'user', None)
        if not target_user or target_user == user:
            return  # Don't notify self or invalid target

        notification_status = {}

        # Check user notification settings
        settings, _ = NotificationSettings.objects.get_or_create(user=target_user)
        if settings.enable_boost_push_notifications:
            # Save notification
            notify.send(
                sender=user,
                recipient=target_user,
                verb=f"Boosted your {boost.content_type.model}",
                action_object=content_object,
            )

            # Send push notification
            send_notification = sendpush(
                title="New Boost",
                msg=f"{user.username} boosted your {boost.content_type.model}",
                registration_token=target_user.fcm_token,
                dataObject={
                    "user": user.username,
                    "type": boost.content_type.model,
                    "id": boost.object_id,
                },
            )

            if send_notification and hasattr(send_notification, "responses"):
                notification_status[f"{user} boosted"] = [
                    i.exception for i in send_notification.responses
                ]
            else:
                notification_status[f"{user} boosted"] = "Notification not sent or no response"
    def get_queryset(self):
        return Boost.objects.filter(user=self.request.user)
    
    @action(detail=False, methods=['post'], url_path='unboost')
    def unboost(self, request):
        serializer = UnboostSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        content_type = serializer.validated_data['content_type']
        object_id = serializer.validated_data['object_id']

        Boost.objects.filter(
            user=request.user,
            content_type=content_type,
            object_id=object_id
        ).delete()

        return Response(status=204)



class MentionViewSet(viewsets.ModelViewSet):
    serializer_class = MentionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Mention.objects.all()
        
    def perform_create(self, serializer):
        # Save the mention
        serializer.save()
        
      
        
    @action(detail=False, methods=["get"], url_path="user/(?P<user_id>[^/.]+)")
    def get_mentions_by_user(self, request, user_id=None):
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response(
                {"detail": "User not found."}, status=status.HTTP_404_NOT_FOUND
            )

        mentions = (
            Mention.objects.filter(user=user)
            .select_related("user", "post", "comment")
            .order_by("-created_at")
        )
        serializer = MentionSerializer(mentions, many=True)
        return Response(serializer.data)

    @action(
        detail=False,
        methods=["get"],
        url_path="post/(?P<post_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def mentions_by_post(self, request, post_id):
        mentions = Mention.objects.filter(post_id=post_id)
        serializer = self.get_serializer(mentions, many=True)
        return Response(serializer.data)

    @action(
        detail=False,
        methods=["get"],
        url_path="comment/(?P<comment_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def mentions_by_comment(self, request, comment_id):
        mentions = Mention.objects.filter(comment_id=comment_id)
        serializer = self.get_serializer(mentions, many=True)
        return Response(serializer.data)

    @action(
        detail=True,
        methods=["put"],
        url_path="read-by-id",
        permission_classes=[IsAuthenticated],
    )
    def mark_single_read(self, request, pk=None):
        mention = get_object_or_404(Mention, pk=pk)

        is_read = request.data.get("is_read")
        if is_read is not None:
            mention.is_read = is_read
            mention.save(update_fields=["is_read"])
        else:
            mention.save(update_fields=["is_read"])

        serializer = self.get_serializer(mention)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["put"],
        url_path="mark-all-read-by-user",
        permission_classes=[IsAuthenticated],
    )
    def mark_all_read_by_user(self, request):
        is_read = request.data.get("is_read")
        mentions = Mention.objects.filter(user=request.user)

        if is_read is not None:
            mentions.update(is_read=is_read)
        else:
            mentions.update(is_read=is_read)

        serializer = self.get_serializer(mentions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ReportPostViewSet(viewsets.ModelViewSet):
    serializer_class = ReportPostSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def get_queryset(self):
        return (
            ReportPost.objects.filter(user=self.request.user)
            if self.request.user.is_authenticated
            else ReportPost.objects.none()
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def post_action_view(request, *args, **kwargs):
    """
    id is required.
    Action Options Are: like, unlike, boost
    """
    user = request.user
    serializer = PostActionSerializer(data=request.POST)
    notification_status = {}
    notification_settings_get_and_check = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    notification_settings = notification_settings_get_and_check[0]
    if serializer.is_valid(raise_exception=True):
        data = serializer.validated_data
        post_id = data.get("post_id")
        action = data.get("action")
        content = data.get("content")
        queryset = Post.objects.filter(id=post_id)
        if not queryset.exists():
            return Response({}, status=404)
        obj = queryset.first()
        if action == "like":
            obj.likes.add(user)
            if notification_settings.likes_push_notifications:
                # Save to Notifications model
                notify.send(
                    sender=user,
                    recipient=obj.user,
                    verb="Liked your post",
                    action_object=obj,
                )
                # Send Firebase Notification
                send_notification = sendpush(
                    title="New Like",
                    msg=f"{user} liked your post",
                    registration_token=user.fcm_token,
                    dataObject={"user": f"{user}"},
                )
                if send_notification and hasattr(send_notification, "responses"):
                    notification_status[f"{user} liked"] = (
                        "\n" + f"{[i.exception for i in send_notification.responses]}"
                    )

                else:
                    notification_status[
                        f"{user} liked"
                    ] = "Notification not sent or no response"
            return Response(
                {
                    "status": "Liked",
                    "notification_status": notification_status,
                    "code": 200,
                },
                status.HTTP_200_OK,
            )
        elif action == "unlike":
            obj.likes.remove(user)
            return Response({"status": "Like Removed", "code": 200}, status.HTTP_200_OK)

        # elif action == "boost":
        #     # Step 1: Resolve the original (non-boosted) post
        #     original_post = obj.parent if obj.is_boost and obj.parent else obj

        #     if original_post.user == user:
        #         return Response(
        #             {
        #                 "message": "You cannot boost your own post"
        #             },
        #             status= status.HTTP_400_BAD_REQUEST
        #         )

        #     # Step 2: Check if this user already boosted this original post
        #     already_boosted = Post.objects.filter(
        #         user=user, parent=original_post, is_boost=True
        #     ).exists()

        #     if already_boosted:
        #         return Response(
        #             {
        #                 "status": "Already Boosted",
        #                 "message": "You have already boosted this post.",
        #                 "code": 400,
        #             },
        #             status=400,
        #         )

        #     # Step 3: Create the boost
        #     new_post = Post.objects.create(
        #         user=user,
        #         parent=original_post,
        #         is_boost=True,
        #         content=request.data.get("content", ""),
        #     )

        #     serializer = PostSerializer(new_post)

        #     # Step 4: Send notifications (if enabled)
        #     if notification_settings.enable_boost_push_notifications:
        #         notify.send(
        #             sender=user,
        #             recipient=original_post.user,
        #             verb="Boosted your post",
        #             action_object=original_post,
        #         )

        #         send_notification = sendpush(
        #             title="Post Boosted",
        #             msg=f"{user} boosted your post",
        #             registration_token=original_post.user.fcm_token,
        #             dataObject={"post_id": new_post.id},
        #         )

        #         if send_notification and hasattr(send_notification, "responses"):
        #             notification_status[f"{user} boosted"] = [
        #                 str(i.exception)
        #                 for i in send_notification.responses
        #                 if i.exception
        #             ]
        #         else:
        #             notification_status[
        #                 f"{user} boosted"
        #             ] = "Notification not sent or no response"

        #     return Response(
        #         {
        #             "data": serializer.data,
        #             "notification_status": notification_status,
        #             "status": "Boosted",
        #             "code": 200,
        #         },
        #         status=200,
        #     )
        elif action == "uninterested":
            UninterestedPost.objects.create(user=user, post=obj)
            return Response({"status": "Uninterested Post Excludued", "code": 200})

        return Response({}, 200)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def comment_action_view(request, *args, **kwargs):
    """
    id is required.
    Action Options Are: like, unlike, boost
    """
    user = request.user
    serializer = CommentActionSerializer(data=request.POST)
    notification_status = {}
    notification_settings_get_and_check = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    notification_settings = notification_settings_get_and_check[0]
    if serializer.is_valid(raise_exception=True):
        data = serializer.validated_data
        comment_id = data.get("comment_id")
        action = data.get("action")
        content = data.get("content")
        obj = get_object_or_404(Comment, id=comment_id)
        if action == "like":
            obj.likes.add(user)
            if notification_settings.likes_push_notifications:
                # Save to Notifications model
                notify.send(
                    sender=user,
                    recipient=obj.user,
                    verb="Liked your comment",
                    action_object=obj,
                )
                # Send Firebase Notification
                send_notification = sendpush(
                    title="New Like",
                    msg=f"{user} liked your comment",
                    registration_token=user.fcm_token,
                    dataObject={"user": f"{user}", "comment": f"{obj}"},
                )
                if send_notification and hasattr(send_notification, "responses"):
                    notification_status[f"{user} liked"] = (
                        "\n" + f"{[i.exception for i in send_notification.responses]}"
                    )

                else:
                    notification_status[
                        f"{user} liked"
                    ] = "Notification not sent or no response"
            return Response(
                {
                    "status": "Liked",
                    "notification_status": notification_status,
                    "code": 200,
                },
                status.HTTP_200_OK,
            )
        elif action == "unlike":
            obj.likes.remove(user)
            return Response({"status": "Like Removed", "code": 200}, status.HTTP_200_OK)
        return Response({}, status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def post_feed_view(request):
    user = request.user
    interests = Interest.objects.values_list("user", flat=True)

    if user.id not in interests:
        return Response({"User Has No Interest"}, status.HTTP_409_CONFLICT)

    page = int(request.GET.get("page", 1))
    page_size = 10
    start = (page - 1) * page_size
    end = start + page_size

    blocked_users = user.blocked_users.values_list("id", flat=True)
    muted_users = user.muted_users.values_list("id", flat=True)
    reported_posts = user.reported_post.values_list("post__id", flat=True)
    users_to_exclude = list(blocked_users) + list(muted_users)

    post_type = ContentType.objects.get_for_model(Post)
    comment_type = ContentType.objects.get_for_model(Comment)

    # --- 1. Get all boosts for posts (exclude spam/blocked)
    boost_qs = Boost.objects.filter(content_type=post_type).exclude(user__id__in=users_to_exclude)
    boost_qs = boost_qs.select_related("user")

    boosted_post_ids = boost_qs.values_list("object_id", flat=True)
    boosted_posts = Post.objects.filter(id__in=boosted_post_ids).exclude(
        Q(user__id__in=users_to_exclude) | Q(id__in=reported_posts)
    )

    # --- 2. Prepare boost map (post_id → list of boosts)
    boost_map = defaultdict(list)
    for boost in boost_qs:
        boost_map[boost.object_id].append(boost)

    boosted_feed_items = []
    for post in boosted_posts:
        # Use only one boost for feed view (latest or first)
        boosts = sorted(boost_map.get(post.id), key=lambda b: b.created_at, reverse=True)
        if boosts:
            boost = boosts[0]  # latest boost
            post._boosted_by_user = boost.user  # sets 'is_boosted' and 'boosted_by'
            serializer = PostSerializer(post, context={"request": request})
            boosted_feed_items.append({
                "id": f"feed_item_boost_{boost.id}",
                "type": "boost",
                "boost_info": {
                    "booster": UserSerializer(boost.user).data,
                    "boost_date": boost.created_at
                },
                "original_post": serializer.data
            })

    # --- 2b. Get all boosts for comments (exclude spam/blocked)
    comment_boost_qs = Boost.objects.filter(content_type=comment_type).exclude(user__id__in=users_to_exclude)
    comment_boost_qs = comment_boost_qs.select_related("user")

    boosted_comment_ids = comment_boost_qs.values_list("object_id", flat=True)
    boosted_comments = Comment.objects.filter(id__in=boosted_comment_ids).exclude(
        user__id__in=users_to_exclude
    )
    # If you add comment reporting, also exclude reported comments here

    # Prepare boost map for comments
    comment_boost_map = defaultdict(list)
    for boost in comment_boost_qs:
        comment_boost_map[boost.object_id].append(boost)

    boosted_comment_feed_items = []
    for comment in boosted_comments:
        boosts = sorted(comment_boost_map.get(comment.id), key=lambda b: b.created_at, reverse=True)
        if boosts:
            boost = boosts[0]  # latest boost
            comment._boosted_by_user = boost.user
            serializer = CommentSerializer(comment, context={"request": request})
            boosted_comment_feed_items.append({
                "id": f"feed_item_boost_comment_{boost.id}",
                "type": "boost_comment",
                "boost_info": {
                    "booster": UserSerializer(boost.user).data,
                    "boost_date": boost.created_at
                },
                "original_comment": serializer.data
            })

    # --- 3. Get normal posts (exclude ones already shown via boost)
    normal_posts = Post.objects.exclude(
        Q(user__id__in=users_to_exclude) | Q(id__in=reported_posts) | Q(id__in=boosted_post_ids)
    ).order_by("-post_date")

    normal_post_items = []
    for post in normal_posts:
        serializer = PostSerializer(post, context={"request": request})
        normal_post_items.append({
            "id": f"feed_item_post_{post.id}",
            "type": "post",
            "original_post": serializer.data
        })

    # --- 4. Combine, sort and paginate
    full_feed = boosted_feed_items + boosted_comment_feed_items + normal_post_items
    full_feed.sort(key=get_sort_date, reverse=True)

    paginated_feed = full_feed[start:end]

    return Response({
        "results": paginated_feed,
        "page": page,
        "has_next": end < len(full_feed),
    }, status=status.HTTP_200_OK)


class CollectionsViewset(viewsets.ModelViewSet):
    serializer_class = CollectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if getattr(
            self, "swagger_fake_view", False
        ):  # drf-yasg schema generation check
            return Collection.objects.none()
        return Collection.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
        return super().perform_create(serializer)

    @action(detail=False, methods=["get"], permission_classes=[IsAuthenticated])
    def collection_posts(self, request):
        collection_id = request.GET.get("id")
        if not collection_id:
            return Response(
                {"error": "Collection ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            saved_posts = SavedPost.objects.filter(
                collection_id=collection_id, collection__user=self.request.user
            ).select_related("post")
            posts = [saved_post.post for saved_post in saved_posts]
            serialized_posts = PostSerializer(posts, many=True).data
            return Response(serialized_posts, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SavedPostViewset(viewsets.ModelViewSet):
    serializer_class = SavedPostSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if getattr(
            self, "swagger_fake_view", False
        ):  # drf-yasg schema generation check
            return SavedPost.objects.none()
        return SavedPost.objects.filter(collection__user=self.request.user)
