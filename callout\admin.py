from django.contrib import admin
from .models import CallOut, CallOutAction, CallOutMedia, Response, Callout_Choice, Intent
from unfold.admin import ModelAdmin

class CallOutAdmin(ModelAdmin):
    list_display = ["user", "followed_user", "message", "call_out_date"]
    search_fields = ["message", "user__username", "followed_user__username"]
    list_filter = ["call_out_date"]

class CallOutActionAdmin(ModelAdmin):
    list_display = ["call_out", "user", "action"]
    search_fields = ["call_out__message", "user__username"]
    list_filter = ["action"]

class CallOutMediaAdmin(ModelAdmin):
    list_display = ["call_out", "media"]
    search_fields = ["call_out__message"]

class ResponseAdmin(ModelAdmin):
    list_display = ["call_out", "user", "message", "response_time"]
    search_fields = ["message", "user__username", "call_out__message"]
    list_filter = ["response_time"]

class Callout_ChoiceAdmin(ModelAdmin):
    list_display = ["title"]
    search_fields = ["title"]

class IntentAdmin(ModelAdmin):
    list_display = ["title"]
    search_fields = ["title"]

admin.site.register(CallOut, CallOutAdmin)
admin.site.register(CallOutAction, CallOutActionAdmin)
admin.site.register(CallOutMedia, CallOutMediaAdmin)
admin.site.register(Response, ResponseAdmin)
admin.site.register(Callout_Choice, Callout_ChoiceAdmin)
admin.site.register(Intent, IntentAdmin)
