from django.contrib import admin
from .models import (
    Post,
    PostLike,
    PostImage,
    ReportPost,
    SavedPost,
    Collection,
    Mention,
    Comment,
)
from unfold.admin import ModelAdmin, TabularInline

class PostLikeAdmin(TabularInline):
    model = PostLike


class PostImageAdmin(TabularInline):
    model = PostImage


# @admin.register(Post)
class PostAdmin(ModelAdmin):
    inlines = [PostLikeAdmin, PostImageAdmin]
    list_display = ("__str__", "user", "content", "post_date")
    search_fields = ("user__email", "user__username")
    list_filter = ("user__is_staff",)

    class Meta:
        model = Post


class PostImageAdmin(ModelAdmin):
    list_display = ["post", "media"]


class PostLikeAdmin(ModelAdmin):
    list_display = ["post", "user", "created_at"]
    search_fields = ["post__content", "user__username"]


class ReportPostAdmin(ModelAdmin):
    list_display = ["post", "user", "reason", "created_at"]
    list_filter = ["reason", "created_at"]
    search_fields = ["post__content", "user__username"]


class SavedPostAdmin(ModelAdmin):
    list_display = ["post", "collection"]
    search_fields = ["post__content", "collection__name", "collection__user__username"]
    list_filter = ["collection"]


class CollectionAdmin(ModelAdmin):
    list_display = ["name", "user"]
    search_fields = ["name", "user__username"]


class MentionAdmin(ModelAdmin):
    list_display = ["post", "user", "created_at"]
    search_fields = ["post__content", "user__username"]


class CommentAdmin(ModelAdmin):
    list_display = ["post", "user", "content", "timestamp"]
    search_fields = ["content", "user__username", "post__content"]


admin.site.register(Post, PostAdmin)
admin.site.register(PostImage, PostImageAdmin)
admin.site.register(PostLike, PostLikeAdmin)
admin.site.register(ReportPost, ReportPostAdmin)
admin.site.register(SavedPost, SavedPostAdmin)
admin.site.register(Collection, CollectionAdmin)
admin.site.register(Mention, MentionAdmin)
admin.site.register(Comment, CommentAdmin)






from django_celery_beat.admin import PeriodicTaskAdmin
from django_celery_beat.models import PeriodicTask

admin.site.unregister(PeriodicTask)  # Unregister first to customize


@admin.register(PeriodicTask)
class CustomPeriodicTaskAdmin(PeriodicTaskAdmin):
    list_display = ("name", "enabled", "interval", "crontab", "solar", "one_off")
