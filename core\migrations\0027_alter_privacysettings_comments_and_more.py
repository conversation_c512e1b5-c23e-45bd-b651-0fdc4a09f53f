# Generated by Django 4.2.1 on 2025-06-15 17:42

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0026_alter_verification_account_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="privacysettings",
            name="comments",
            field=models.CharField(
                choices=[
                    ("everyone", "Everyone"),
                    ("no_one", "No One"),
                    ("following", "Following"),
                    ("followers_and_following", "Followers and Following"),
                ],
                default="everyone",
                max_length=30,
            ),
        ),
        migrations.AlterField(
            model_name="privacysettings",
            name="tags",
            field=models.CharField(
                choices=[
                    ("everyone", "Everyone"),
                    ("no_one", "No One"),
                    ("following", "Following"),
                    ("followers_and_following", "Followers and Following"),
                ],
                default="everyone",
                max_length=30,
            ),
        ),
    ]
