# Generated by Django 4.2.1 on 2024-01-20 21:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("post", "0004_rename_collection_savedpost_collection"),
    ]

    operations = [
        migrations.CreateModel(
            name="ReportPostOffence",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name="ReportPost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("misinformation", "Misinformation/Fake News"),
                            ("discrimination", "Hate Speech and Discrimination"),
                            ("violence-threat", "Threats of Violence Against Others"),
                            ("self-harm-thread", "Threats of Self-Harm"),
                            ("harassment", "Harrassment & Bullying"),
                            ("nudity", "Nudity & Sexual Content"),
                            ("spam", "Spam & Irrelivant Content"),
                        ],
                        max_length=50,
                    ),
                ),
                ("additional_comment", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reported_post",
                        to="post.post",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reporter",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
