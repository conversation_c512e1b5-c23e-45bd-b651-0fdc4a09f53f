/* Django Unfold Admin Custom Styles */

/* Form Fields */
.unfold .form-control,
.unfold input[type="text"],
.unfold input[type="password"],
.unfold input[type="email"],
.unfold input[type="number"],
.unfold input[type="url"],
.unfold input[type="date"],
.unfold input[type="datetime-local"],
.unfold input[type="time"],
.unfold input[type="search"],
.unfold textarea,
.unfold select,
.unfold-form-control {
    border-radius: 0.5rem !important;
    border: 1px solid #D1D5DB !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    padding: 0.5rem 0.75rem !important;
}

.unfold-form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px #bfdbfe;
    outline: none;
}

/* Form Labels */
.unfold-form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

/* Buttons */
.unfold-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    background-color: #2563eb;
    transition: all 0.2s ease-in-out;
}

.unfold-button:hover {
    background-color: #1d4ed8;
}

.unfold-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #93c5fd;
}

.unfold-button-secondary {
    background-color: white;
    border-color: #D1D5DB;
    color: #374151;
}

.unfold-button-secondary:hover {
    background-color: #F9FAFB;
}

/* Cards and Panels */
.unfold-card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
    transition: box-shadow 0.2s ease-in-out;
}

.unfold-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Tables */
.unfold-table {
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-bottom: 1px solid #E5E7EB;
}

.unfold-table th {
    padding: 0.75rem 1.5rem;
    background-color: #F9FAFB;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6B7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.unfold-table td {
    padding: 1rem 1.5rem;
    white-space: nowrap;
    font-size: 0.875rem;
    color: #111827;
}

.unfold-table tr:nth-child(even) {
    background-color: #F9FAFB;
}

/* Dashboard Widgets */
.unfold-widget {
    padding: 1.5rem;
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
}

.unfold-widget-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 1rem;
}

/* Navigation */
.unfold-nav-item {
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    transition: all 0.2s ease-in-out;
}

.unfold-nav-item:hover {
    color: #111827;
    background-color: #F3F4F6;
}

.unfold-nav-item.active {
    background-color: #EFF6FF;
    color: #2563EB;
}

/* Alerts and Messages */
.unfold-alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.unfold-alert-success {
    background-color: #F0FDF4;
    color: #166534;
    border: 1px solid #BBF7D0;
}

.unfold-alert-error {
    background-color: #FEF2F2;
    color: #991B1B;
    border: 1px solid #FECACA;
}

/* Pagination */
.unfold-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
}

.unfold-page-link {
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    background-color: white;
    border: 1px solid #D1D5DB;
}

.unfold-page-link:hover {
    background-color: #F9FAFB;
}

.unfold-page-link.active {
    background-color: #2563EB;
    color: white;
    border-color: #2563EB;
}

/* Select Fields */
select.unfold-form-control {
    padding-left: 0.75rem;
    padding-right: 2.5rem;
    font-size: 1rem;
    border: 1px solid #D1D5DB;
    border-radius: 0.375rem;
}

select.unfold-form-control:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 2px #BFDBFE;
}

/* Checkboxes and Radio Buttons */
.unfold-checkbox {
    height: 1rem;
    width: 1rem;
    color: #2563EB;
    border: 1px solid #D1D5DB;
    border-radius: 0.25rem;
}

.unfold-radio {
    height: 1rem;
    width: 1rem;
    color: #2563EB;
    border: 1px solid #D1D5DB;
    border-radius: 50%;
}

/* File Upload */
.unfold-file-upload {
    display: flex;
    justify-content: center;
    padding: 1.5rem;
    border: 2px dashed #D1D5DB;
    border-radius: 0.5rem;
}

/* Date and Time Inputs */
input[type="date"].unfold-form-control,
input[type="time"].unfold-form-control,
input[type="datetime-local"].unfold-form-control {
    padding-left: 0.75rem;
    padding-right: 2.5rem;
}

/* Tooltips */
.unfold-tooltip {
    position: absolute;
    z-index: 10;
    padding: 0.5rem;
    font-size: 0.875rem;
    color: white;
    background-color: #111827;
    border-radius: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease-in-out;
}

.unfold-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Rich Text Editor */
.unfold-rich-text-editor {
    margin-top: 0.25rem;
    display: block;
    width: 100%;
    border-radius: 0.5rem;
    border: 1px solid #D1D5DB;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    min-height: 120px;
}

/* Admin Login Form */
.login #content-main {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    padding: 2rem;
    background-color: white;
}

.login #content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f3f4f6;
}

/* Make the form inputs match the container style */
.login .form-row input {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border-radius: 0.375rem;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
    .unfold-form-control,
    .unfold-button {
        width: 100%;
    }
    
    .unfold-table {
        table-layout: auto;
    }
    
    .unfold-widget {
        padding: 1rem;
    }
}
