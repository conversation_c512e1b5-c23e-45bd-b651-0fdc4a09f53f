from django.shortcuts import render, get_object_or_404
from rest_framework.viewsets import ModelViewSet
from .serializer import PaymentSerializer
from .models import Payment
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action


class PaymentViewSet(ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
        return super().perform_create(serializer)

    @action(detail=False, method=["GET"], permission_classes=[IsAuthenticated])
    def verify_payment(self, request):
        ref = request.GET.get("ref")
        payment = get_object_or_404(Payment, ref=ref)
        verified = payment.verify_payment()
