from django.db import models

# from post.models import Post
from core.models import User
from django_countries.fields import CountryField
from core.models import InterestChoice
from post.models import Post
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.fields import ArrayField


class CountryOption(models.Model):
    code = CountryField(unique=True)

    def __str__(self):
        return self.code.name


class AD_BUTTON_CHOICES(models.TextChoices):
    send_dm = "send_dm", _("SendDm")
    view_profile = "view_profile", _("ViewProfile")
    connect = "connect", _("Connect")

    def __str__(self) -> str:
        return super().__str__()


class Advert(models.Model):
    post = models.ForeignKey(
        Post, on_delete=models.CASCADE, related_name="sponsored_post"
    )
    advertiser = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="advert"
    )
    # preferred_location_reach = models.ManyToManyField(LocationPreferences, related_name='advert')
    preferred_location_reach = preferred_location_reach = models.ManyToManyField(
        CountryOption,
        related_name="adverts",
        blank=True,
        help_text="Select one or more target countries",
    )
    promotion_start_date = models.DateField()
    promotion_end_date = models.DateField()
    audience_interests = models.ManyToManyField(InterestChoice, blank=True)
    age_range_min = models.IntegerField(default=18)
    age_range_max = models.IntegerField(default=65)
    ad_button = models.CharField(max_length=50, choices=AD_BUTTON_CHOICES.choices)
    paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return f"Ad by {self.advertiser} for post {self.post_id}"


class AdImpression(models.Model):
    ad = models.ForeignKey(
        "Advert", on_delete=models.CASCADE, related_name="impressions"
    )
    viewer = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="ad_impressions"
    )
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["ad", "viewer"]

    def __str__(self):
        return f"Impression on {self.ad} by {self.viewer}"

    # Analytics Fields

    # Proxy model? NO ITS ONLY FOR CHANGING THE PYTHONIC BEHAVIOUR OF AN EXISTING MODEL
