# Generated by Django 4.2.1 on 2025-06-04 12:08

import cloudinary.models
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0022_user_cover_photo"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="verification",
            name="additional_docs",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="address_proof",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="board_resolution",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="business_reg",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="dob",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="govt_entity_docs",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="govt_legitimacy_doc",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="govt_request",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="legal_business_name",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="photograph",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="request_reason",
        ),
        migrations.RemoveField(
            model_name="verification",
            name="tin",
        ),
        migrations.AddField(
            model_name="verification",
            name="account_type",
            field=models.CharField(
                choices=[
                    ("personal", "Personal"),
                    ("business", "Business/Organization"),
                    ("government", "Government"),
                    ("official", "Government Official"),
                ],
                default="official",
                max_length=20,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="verification",
            name="additional_info",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="verification",
            name="business_address_proof",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Business Address Proof",
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="business_doc",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Business Registration Document",
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="business_name",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="verification",
            name="business_premises_photo",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Business Premises Photo",
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="verification",
            name="date_of_birth",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="verification",
            name="govt_account_verification_letter",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Verification Letter",
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="govt_agency",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="verification",
            name="govt_authorized_id",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Authorized Identification",
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="govt_registration_docs",
            field=cloudinary.models.CloudinaryField(
                blank=True, max_length=255, null=True, verbose_name="Registration Docs"
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="official_id_card",
            field=cloudinary.models.CloudinaryField(
                blank=True, max_length=255, null=True, verbose_name="Official ID Card"
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="official_letter",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Official Letter or Regulatory Documentation",
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="proof_of_address",
            field=cloudinary.models.CloudinaryField(
                blank=True, max_length=255, null=True, verbose_name="Proof of Address"
            ),
        ),
        migrations.AddField(
            model_name="verification",
            name="reason_for_verification",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="verification",
            name="recent_photo",
            field=cloudinary.models.CloudinaryField(
                blank=True, max_length=255, null=True, verbose_name="Recent Photograph"
            ),
        ),
        migrations.AlterField(
            model_name="verification",
            name="category",
            field=models.CharField(
                blank=True,
                choices=[
                    ("personal", "Personal"),
                    ("politics", "Politics"),
                    ("entertainment", "Entertainment"),
                    ("government", "Government"),
                    ("sports", "Sports"),
                    ("news", "News/Media"),
                    ("other", "Other"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="verification",
            name="government_id",
            field=cloudinary.models.CloudinaryField(
                blank=True,
                max_length=255,
                null=True,
                verbose_name="Government Issued ID",
            ),
        ),
    ]
