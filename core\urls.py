from django.urls import path
from .views import (
    PasswordResetMail,
    VerifyCode,
    ChangePassword,
    EmailTokenObtainPairView,
    InterestChoiceView,
    InterestView,
    action_view,
    VerificationRequest,
    UserViewSet,
    PrivacySettingsViewSet,
    NotificationPreferenceViewSet,
    BlogViewSet,
    NotificationViewSet,
    follow_view,
)

from rest_framework_simplejwt.views import TokenRefreshView

from rest_framework.routers import DefaultRouter


app_name = "core"

router = DefaultRouter()
router.register(r"interest-choice", InterestChoiceView, basename="interest-choice")
router.register(r"user-interest", InterestView, basename="user-interest")
router.register(r"user", UserViewSet, basename="User")
router.register(r"privacy-settings", PrivacySettingsViewSet, basename="Privacy")
router.register(r"blog", BlogViewSet, basename="blog")
router.register(
    r"notifications-settings",
    NotificationPreferenceViewSet,
    basename="notifications-settings",
)
router.register(r"notifications", NotificationViewSet, basename="notification")

urlpatterns = [
    path("token/", EmailTokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    # path("countries", return_countries, name="countries"),
    path(
        "password-reset-mail", PasswordResetMail.as_view(), name="password-reset-mail"
    ),
    path("reset-password", ChangePassword.as_view(), name="passwrd-reset"),
    path("verify", VerifyCode.as_view(), name="verify-code"),
    path(
        "request-verification",
        VerificationRequest.as_view(),
        name="request-verification",
    ),
    path("<str:username>/user-actions", action_view, name="user-follow"),
    path("follow_data", follow_view, name="follow_data"),
]
urlpatterns += router.urls
