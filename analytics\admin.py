from django.contrib import admin
from .models import Follower<PERSON>ount, MentionCount, AccountsPerformance, ProfileViewTrack
from django.utils import timezone


class FollowerCountAdmin(admin.ModelAdmin):
    readonly_fields = ("count",)  # Set the 'count' field as read-only
    list_display = (
        "user",
        "follower",
        "count",
        "timestamp",
    )  # Show in the list display
    search_fields = ("user__username", "follower__username")
from unfold.admin import ModelAdmin


class FollowerCountAdmin(ModelAdmin):
    readonly_fields = ('count',)  # Set the 'count' field as read-only
    list_display = ('user', 'follower', 'count', 'timestamp')  # Show in the list display
    search_fields = ('user__username', 'follower__username')


class MentionCountAdmin(ModelAdmin):
    list_display = ['user', 'count', 'timestamp']
    search_fields = ['user__username']
    list_filter = ['timestamp']


class AccountsPerformanceAdmin(ModelAdmin):
    list_display = ['user', 'like_count', 'comment_count', 'boost_count', 'mentions_count', 'timestamp']
    search_fields = ['user__username']
    list_filter = ['timestamp']


class ProfileViewTrackAdmin(ModelAdmin):
    list_display = ['user', 'viewed_by', 'timestamp']
    search_fields = ['user__username', 'viewed_by__username']
    list_filter = ['timestamp']


admin.site.register(FollowerCount, FollowerCountAdmin)
admin.site.register(MentionCount, MentionCountAdmin)
admin.site.register(AccountsPerformance, AccountsPerformanceAdmin)
admin.site.register(ProfileViewTrack, ProfileViewTrackAdmin)



