# Generated by Django 4.2.1 on 2023-11-08 14:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("chat", "0002_connection"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="connection",
            name="reciever",
        ),
        migrations.AddField(
            model_name="connection",
            name="receiver",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="received_connections",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=False,
        ),
    ]
