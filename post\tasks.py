from celery import shared_task
from post.models import Post
from django.utils import timezone
import logging
from django.db import models
from django.db.models import Q


logger = logging.getLogger(__name__)

# @shared_task
# def publish_scheduled_posts():
#     now = timezone.now()
#     posts_to_publish = Post.objects.filter(scheduled_datetime__lte=now, is_published=False)

#     for post in posts_to_publish:
#         try:
#             post.is_published = True
#             post.save()
#             logger.info(f"Post {post.id} has been published.")
#         except Exception as e:
#             logger.error(f"Error publishing post {post.id}: {str(e)}")


@shared_task
def publish_scheduled_posts(post_id=None):
    """
    Task to publish scheduled posts, handling separate date and time fields
    """

    now = timezone.now()
    current_date = now.date()
    current_time = now.time()

    # Build the query for unpublished posts with scheduled datetime <= now
    query = (
        Post.objects.filter(is_published=False)
        .annotate(
            # Combine date and time into a datetime value for comparison
            scheduled_datetime=models.ExpressionWrapper(
                models.functions.Cast(
                    models.F("scheduled_date"), models.DateTimeField()
                )
                + models.functions.Cast(
                    models.F("scheduled_time"), models.DurationField()
                ),
                output_field=models.DateTimeField(),
            )
        )
        .filter(scheduled_datetime__lte=now)
    )

    if post_id:
        query = query.filter(id=post_id)

    published_count = 0
    for post in query:
        try:
            post.is_published = True
            post.save()
            published_count += 1
            logger.info(
                f"Published post {post.id} (scheduled for {post.scheduled_date} {post.scheduled_time})"
            )
        except Exception as e:
            logger.error(f"Error publishing post {post.id}: {str(e)}")

    logger.info(f"Published {published_count} scheduled posts at {now}")
    return published_count


@shared_task
def check_missed_scheduled_posts():
    """Backup task in case any scheduled posts were missed"""

    posts = Post.objects.filter(
        is_published=False, scheduled_date__isnull=False, scheduled_time__isnull=False
    ).extra(where=["scheduled_date + scheduled_time <= %s"], params=[timezone.now()])

    for post in posts:
        post.is_published = True
        post.save()
