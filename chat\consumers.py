from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
import json
from core.models import User, NotificationSettings
from django.db.models import Q, OuterRef, Exists
from django.db.models.functions import Coalesce
from .serializers import (
    SearchSerializer,
    RequestSerializer,
    FriendSerializer,
    MessageSerializer,
)
from core.FCMManager import sendpush
from core.serializers import UserSerializer
from .models import Connection, Message
import logging


logger = logging.getLogger(__name__)


class ChatConsumer(WebsocketConsumer):
    def connect(self):
        """Handle WebSocket connection"""
        logger.info(self.scope)
        self.user = self.scope["user"]
        if not self.user.is_authenticated:
            logger.warning("Unauthenticated user connection attempt")
            # Close connection for unauthenticated users
            self.close()
            return

        # Save username as a groupname for this user
        self.username = self.user.username
        logger.info(f"User connected: {self.username}")

        # Join this user to a group with their username
        async_to_sync(self.channel_layer.group_add)(self.username, self.channel_name)
        self.accept()

    def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        logger.info(f"User disconnected: {getattr(self, 'username', 'unknown')}")
        if hasattr(self, "username"):
            # Leave group
            async_to_sync(self.channel_layer.group_discard)(
                self.username, self.channel_name
            )

    def receive(self, text_data=None, bytes_data=None):
        """Route incoming WebSocket messages to appropriate handlers"""
        try:
            # Receive message from websocket
            data = json.loads(text_data)
            logger.info("Received WebSocket message:\n%s", json.dumps(data, indent=2))

            data_source = data.get("source")
            # Route messages to appropriate handlers based on source
            handlers = {
                "message.list": self.receive_message_list,
                "friends.list": self.receive_friends_list,
                "message.read": self.receive_message_read,
                "message.type": self.receive_message_type,
                "message.send": self.receive_message_send,
                "request.accept": self.receive_request_accept,
                "request.list": self.receive_request_list,
                "search": self.receive_search,
                "request.connect": self.receive_request_connect,
            }

            handler = handlers.get(data_source)
            if handler:
                handler(data)
            else:
                logger.warning(f"Unknown message source: {data_source}")

        except json.JSONDecodeError:
            logger.error("Error decoding JSON data")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}", exc_info=True)

    def receive_message_list(self, data):
        """Handle message list request"""
        try:
            user = self.user
            connection_id = data.get("connectionId")
            page = data.get("page", 0)
            page_size = 8

            # Get Connection
            connection = self._get_connection_by_id(connection_id)
            if not connection:
                return

            # Get Messages with pagination
            messages = Message.objects.filter(connection=connection).order_by(
                "-created"
            )[page * page_size : (page + 1) * page_size]

            # Serialize messages
            serialized_messages = MessageSerializer(
                messages, context={"user": user}, many=True
            )

            # Get recipient (the other user in the conversation)
            recipient = (
                connection.receiver if connection.sender == user else connection.sender
            )
            serialized_friend = UserSerializer(recipient)

            # Count total messages and determine if there's a next page
            total_messages = Message.objects.filter(connection=connection).count()
            next_page = page + 1 if total_messages > (page + 1) * page_size else None

            data = {
                "messages": serialized_messages.data,
                "next": next_page,
                "friend": serialized_friend.data,
            }

            self.send_group(user.username, "message.list", data)
        except Exception as e:
            logger.error(f"Error processing message list: {e}", exc_info=True)

    def receive_message_type(self, data):
        """Handle typing indicator"""
        try:
            user = self.user
            recipient_username = data.get("username")
            if not recipient_username:
                logger.warning("No recipient username provided for typing notification")
                return

            # Send typing notification to recipient
            data = {"username": user.username}
            self.send_group(recipient_username, "message.type", data)
        except Exception as e:
            logger.error(f"Error processing typing notification: {e}", exc_info=True)

    def receive_message_read(self, data):
        """Handle message read status updates"""
        try:
            user = self.user
            recipient_username = data.get("username")
            message_ids = data.get("messageIDs", [])

            if not isinstance(message_ids, list) or not message_ids:
                logger.warning("No message IDs provided or invalid format")
                return

            if not recipient_username:
                logger.warning("No recipient username provided")
                return

            # Update read status for messages
            updated_count = Message.objects.filter(
                id__in=message_ids, is_read=False
            ).update(is_read=True)

            logger.info(f"{updated_count} messages marked as read")

            # Prepare response data
            read_data = {
                "username": user.username,
                "read": True,
                "messageIDs": message_ids,
            }

            # Notify both users about read status
            self.send_group(recipient_username, "message.read", read_data)
            self.send_group(user.username, "message.read", read_data)
        except Exception as e:
            logger.error(f"Error processing message read status: {e}", exc_info=True)

    def receive_message_send(self, data):
        """Handle sending new messages"""
        try:
            user = self.user
            connection_id = data.get("connectionId")
            message_text = data.get("message")
            message_image = data.get("image")
            message_video = data.get("video")
            message_post = data.get("post")

            # Get Connection
            connection = self._get_connection_by_id(connection_id)
            if not connection:
                return

            # Create message
            message = Message.objects.create(
                connection=connection,
                user=user,
                text=message_text,
                post=message_post,
                image=message_image,
                video=message_video,
            )

            # Get recipient (the other user in the conversation)
            recipient = (
                connection.receiver if connection.sender == user else connection.sender
            )

            # Send message to sender (confirmation)
            self._send_message_to_user(message, user, recipient)

            # Send message to recipient
            self._send_message_to_user(message, recipient, user)

            # Send notification if enabled
            self._send_push_notification(recipient, connection, message)

        except Exception as e:
            logger.error(f"Error sending message: {e}", exc_info=True)

    def receive_friends_list(self, data):
        """Handle friends list request"""
        try:
            user = self.user

            # Latest Message Subquery
            latest_message = Message.objects.filter(connection=OuterRef("id")).order_by(
                "-created"
            )[:1]

            # Get Connections for the user with latest message info
            connections = (
                Connection.objects.filter(
                    Q(sender=user) | Q(receiver=user), accepted=True
                )
                .annotate(
                    latest_text=latest_message.values("text"),
                    latest_created=latest_message.values("created"),
                )
                .order_by(Coalesce("latest_created", "updated").desc())
            )

            serialized = FriendSerializer(
                connections, context={"user": user}, many=True
            )

            # Send friends list to requesting user
            self.send_group(user.username, "friends.list", serialized.data)
        except Exception as e:
            logger.error(f"Error processing friends list: {e}", exc_info=True)

    def receive_request_accept(self, data):
        """Handle accepting connection requests"""
        try:
            username = data.get("username")
            if not username:
                logger.warning("No username provided for request accept")
                return

            # Find connection
            try:
                connection = Connection.objects.get(
                    sender__username=username, receiver=self.user, accepted=False
                )
            except Connection.DoesNotExist:
                logger.warning(f"Connection not found for username: {username}")
                return

            # Update connection status
            connection.accepted = True
            connection.save()

            # Serialize request data
            request_data = RequestSerializer(connection).data

            # Notify both users about accepted request
            self.send_group(connection.sender.username, "request.accept", request_data)
            self.send_group(
                connection.receiver.username, "request.accept", request_data
            )

            # Send updated friend data to both users
            self._send_friend_update(connection)

        except Exception as e:
            logger.error(f"Error accepting request: {e}", exc_info=True)

    def receive_request_list(self, data):
        """Handle request list query"""
        try:
            user = self.user
            # Get pending connection requests
            connections = Connection.objects.filter(receiver=user, accepted=False)
            serialized = RequestSerializer(connections, many=True)

            # Send request list to user
            self.send_group(user.username, "request.list", serialized.data)
        except Exception as e:
            logger.error(f"Error processing request list: {e}", exc_info=True)

    def receive_request_connect(self, data):
        """Handle new connection requests"""
        try:
            username = data.get("username")
            sender = self.user

            if not username:
                logger.warning("No username provided for connection request")
                return

            # Get receiver and notification settings
            try:
                receiver = User.objects.get(username=username)
                notification_settings, _ = NotificationSettings.objects.get_or_create(
                    user=receiver
                )
            except User.DoesNotExist:
                logger.warning(f"User not found: {username}")
                return

            # Create connection with appropriate acceptance status
            # Note: There's a bug in the original code using connection.sender before it's defined
            # We'll fix that by using the sender variable directly
            if sender in receiver.followers.all():
                # Auto-accept if sender follows receiver
                connection, created = Connection.objects.get_or_create(
                    sender=sender, receiver=receiver, defaults={"accepted": True}
                )
            else:
                connection, created = Connection.objects.get_or_create(
                    sender=sender, receiver=receiver
                )

            # Serialize connection data
            request_data = RequestSerializer(connection).data

            # Notify both users about new connection request
            self.send_group(connection.sender.username, "request.connect", request_data)
            self.send_group(
                connection.receiver.username, "request.connect", request_data
            )

            # Send push notification if enabled
            if notification_settings.direct_messages_push_notifications:
                self._send_connect_notification(connection, receiver)

        except Exception as e:
            logger.error(f"Error creating connection request: {e}", exc_info=True)

    def receive_search(self, data):
        """Handle user search"""
        try:
            query = data.get("query")
            user = self.user

            if not query:
                logger.info("Empty search query")
                return

            # Get blocked users
            blocked_users = user.blocked_users.all()

            # Search users with connection status annotations
            users = (
                User.objects.filter(
                    ~Q(id__in=blocked_users),
                    Q(username__istartswith=query)
                    | Q(first_name__istartswith=query)
                    | Q(last_name__istartswith=query),
                )
                .exclude(username=self.username)
                .annotate(
                    pending_them=Exists(
                        Connection.objects.filter(
                            sender=user,
                            receiver=OuterRef("id"),
                            accepted=False,
                        )
                    ),
                    pending_me=Exists(
                        Connection.objects.filter(
                            sender=OuterRef("id"),
                            receiver=user,
                            accepted=False,
                        )
                    ),
                    connected=Exists(
                        Connection.objects.filter(
                            Q(sender=user, receiver=OuterRef("id"))
                            | Q(sender=OuterRef("id"), receiver=user),
                            accepted=True,
                        )
                    ),
                )
            )

            # Serialize search results
            serialized = SearchSerializer(users, many=True)

            # Send results to user
            self.send_group(self.username, "search", serialized.data)
        except Exception as e:
            logger.error(f"Error processing search: {e}", exc_info=True)

    # =======================================
    # Helper methods
    # =======================================

    def _get_connection_by_id(self, connection_id):
        """Get connection by ID with error handling"""
        if not connection_id:
            logger.warning("No connection ID provided")
            return None

        try:
            return Connection.objects.get(id=connection_id)
        except Connection.DoesNotExist:
            logger.warning(f"Connection not found: {connection_id}")
            return None

    def _send_message_to_user(self, message, recipient_user, sender_user):
        """Send message data to a specific user"""
        serialized_message = MessageSerializer(
            message, context={"user": recipient_user}
        )
        serialized_friend = UserSerializer(sender_user)

        data = {"message": serialized_message.data, "friend": serialized_friend.data}

        self.send_group(recipient_user.username, "message.send", data)

    def _send_friend_update(self, connection):
        """Send friend updates after accepting connection"""
        # Send to sender
        sender_data = FriendSerializer(
            connection, context={"user": connection.sender}
        ).data
        self.send_group(connection.sender.username, "friend.new", sender_data)

        # Send to receiver
        receiver_data = FriendSerializer(
            connection, context={"user": connection.receiver}
        ).data
        self.send_group(connection.receiver.username, "friend.new", receiver_data)

    def _send_push_notification(self, recipient, connection, message):
        """Send push notification for new message"""
        try:
            notification_settings, _ = NotificationSettings.objects.get_or_create(
                user=recipient
            )

            if (
                notification_settings.direct_messages_push_notifications
                and recipient.fcm_token
            ):
                sendpush(
                    title="New Message",
                    msg=f"{connection.sender.username} Sent a Message",
                    registration_token=recipient.fcm_token,
                    dataObject={"message": f"{message.text}"},
                )
        except Exception as e:
            logger.error(f"Error sending message notification: {e}", exc_info=True)

    def _send_connect_notification(self, connection, receiver):
        """Send push notification for new connection request"""
        try:
            if receiver.fcm_token:
                sendpush(
                    title="New Connect",
                    msg=f"{connection.sender.username} Requests to Connect",
                    registration_token=receiver.fcm_token,
                    dataObject={"sender": connection.sender.username},
                )
        except Exception as e:
            logger.error(f"Error sending connection notification: {e}", exc_info=True)

    def send_group(self, group, source, data):
        """Send message to a channel group"""
        response = {"type": "broadcast_group", "source": source, "data": data}
        async_to_sync(self.channel_layer.group_send)(group, response)

    def broadcast_group(self, data):
        """
        Broadcast handler that's called when a message is sent to a group
        """
        data.pop("type")
        self.send(text_data=json.dumps(data))


# from channels.generic.websocket import WebsocketConsumer
# from asgiref.sync import async_to_sync
# import json
# from core.models import User
# from django.db.models import Q, OuterRef, Exists
# from django.db.models.functions import Coalesce
# from .serializers import (
#     SearchSerializer,
#     RequestSerializer,
#     FriendSerializer,
#     MessageSerializer,
# )
# from core.FCMManager import sendpush

# from notifications.signals import notify
# from django.shortcuts import get_object_or_404
# from core.serializers import UserSerializer
# from core.models import NotificationSettings
# from .models import Connection, Message
# import logging


# logger = logging.getLogger(__name__)


# class ChatConsumer(WebsocketConsumer):
#     def connect(self):
#         logger.info(self.scope)
#         user = self.scope["user"]
#         if not user.is_authenticated:
#             logger.error(f"Error: User Is Not Authenticated")
#             return

#         # save username to user as a groupname for this user
#         self.username = user.username
#         logger.info(self.username)

#         # Join this user to a group with their email
#         async_to_sync(self.channel_layer.group_add)(self.username, self.channel_name)
#         self.accept()

#     def disconnect(self, close_code):
#         # leave group
#         async_to_sync(self.channel_layer.group_discard)(
#             self.username, self.channel_name
#         )

#     # ================
#     # Handle Requests
#     # =================

#     def receive(self, text_data=None, bytes_data=None):
#         try:
#             # Recieve message from websocket
#             data = json.loads(text_data)
#             logger.info(data)
#             data_source = data.get("source")

#             logger.info("receive:\n%s", json.dumps(data, indent=2))

#             # Message List
#             if data_source == "message.list":
#                 self.receive_message_list(data)
#             # Friends List
#             elif data_source == "friends.list":
#                 self.receive_friends_list(data)
#             # user[scope] has read the message
#             elif data_source == "message.read":
#                 self.receive_message_read(data)

#             # user[scope] is typing Message
#             elif data_source == "message.type":
#                 self.receive_message_type(data)

#             # Message Has been sent
#             elif data_source == "message.send":
#                 self.receive_message_send(data)

#             # Accept Requests
#             elif data_source == "request.accept":
#                 self.receive_request_accept(data)

#             # Request List
#             elif data_source == "request.list":
#                 self.receive_request_list(data)

#             # Search / Filter Users
#             elif data_source == "search":
#                 self.receive_search(data)

#             # Make Message Requests
#             elif data_source == "request.connect":
#                 self.receive_request_connect(data)
#         except json.JSONDecodeError:
#             logger.error("Error decoding JSON data")
#         except Exception as e:
#             logger.error(f"Error processing WebSocket message: {e}")

#     def receive_message_list(self, data):
#         user = self.scope["user"]
#         connection_id = data.get("connectionId")
#         page = data.get("page")
#         page_size = 8
#         try:
#             connection = Connection.objects.get(id=connection_id)
#         except Connection.DoesNotExist:
#             logger.info("Error: Connection Not Found")
#             return
#         # Get Messages
#         messages = Message.objects.filter(connection=connection).order_by("-created")[
#             page * page_size : (page + 1) * page_size
#         ]

#         # serialize messages
#         serialized_messages = MessageSerializer(
#             messages, context={"user": user}, many=True
#         )

#         # Get Recipient
#         recipient = connection.sender

#         if connection.sender == user:
#             recipient = connection.receiver

#         # serialize friend
#         serialized_friend = UserSerializer(recipient)

#         # count the total number of messages for this connection
#         messages_count = Message.objects.filter(
#             connection=connection,
#         ).count()
#         next_page = page + 1 if messages_count > (page + 1) * page_size else None
#         data = {
#             "messages": serialized_messages.data,
#             "next": next_page,
#             "friend": serialized_friend.data,
#         }

#         self.send_group(user.username, "message.list", data)

#     def receive_message_type(self, data):
#         user = self.scope["user"]
#         recipient_username = data.get("username")

#         data = {"username": user.username}
#         self.send_group(recipient_username, "message.type", data)

#     def receive_message_read(self, data):
#         user = self.scope["user"]
#         recipient_username = data.get("username")
#         message_ids = data.get("messageIDs", [])  # expects a list

#         if not isinstance(message_ids, list) or not message_ids:
#             logger.info("No message IDs provided")
#             return
#         try:
#             updated_count = Message.objects.filter(id__in=message_ids, is_read=False).update(is_read=True)
#             logger.info(f"{updated_count} messages marked as read")
#         except Message.DoesNotExist:
#             logger.info("Error: Message Doesn't Exist")

#         data = {"username": user.username, "read": True, "messageIDs": message_ids}
#         # send to reciepient
#         self.send_group(recipient_username, "message.read", data)
#         # send to myself
#         self.send_group(user.username, "message.read", data)

#     def receive_message_send(self, data):
#         """
#         Example websocket message:
#         {
#             "type": "message.send",
#             "connectionId": "123",
#             "message": "Hello!",
#             "image": "data:image/jpeg;base64,/9j/4AAQSkZJRg...", # Base64 encoded image
#             "video": "data:video/mp4;base64,AAAAIGZ0eXBpc29t...", # Base64 encoded video
#             "post": null # Post ID if sharing a post
#         }
#         """
#         user = self.scope["user"]
#         connection_id = data.get("connectionId")
#         message_text = data.get("message")
#         # Image should be sent as base64 encoded string from frontend
#         # Frontend should handle resizing/compressing before sending
#         # Backend will handle decoding and storing in Cloudinary
#         # MessageSerializer not needed here since we serialize after creation
#         message_image = data.get("image")
#         # Video should also be base64 encoded from frontend
#         message_video = data.get("video")
#         message_post = data.get("post")

#         try:
#             connection = Connection.objects.get(id=connection_id)
#         except Connection.DoesNotExist:
#             logger.info("Error: Connection Not Found")
#             return

#         message = Message.objects.create(
#             connection=connection,
#             user=user,
#             text=message_text,
#             post=message_post,
#             image=message_image,
#             video=message_video,
#         )

#         # Get Recipient
#         recipient = connection.sender

#         if connection.sender == user:
#             recipient = connection.receiver

#         # Notification Check
#         notification_settings_get_and_check = (
#             NotificationSettings.objects.get_or_create(user=recipient)
#         )
#         notification_settings = notification_settings_get_and_check[0]

#         # Send new Message back to sender
#         serialized_message = MessageSerializer(message, context={"user": user})
#         serialized_friend = UserSerializer(recipient)
#         data = {"message": serialized_message.data, "friend": serialized_friend.data}
#         self.send_group(user.username, "message.send", data)

#         # Send new Message to Reciever
#         serialized_message = MessageSerializer(message, context={"user": recipient})
#         serialized_friend = UserSerializer(user)
#         data = {"message": serialized_message.data, "friend": serialized_friend.data}
#         self.send_group(recipient.username, "message.send", data)
#         if notification_settings.direct_messages_push_notifications:
#             try:
#                 # Save to Notifications model
#                 # notify.send(
#                 #     sender=connection.sender,
#                 #     recipient=recipient,
#                 #     verb=message.text,
#                 #     action_object=message,
#                 # )
#                 # Send Firebase Notification
#                 send_notification = sendpush(
#                     title="New Message",
#                     msg=f"{connection.sender} Sent a Message",
#                     registration_token=recipient.fcm_token,
#                     dataObject={"message": f"{message}"},
#                 )
#             except Exception as e:
#                 logger.error(f"Error sending push notification: {e}")

#     def receive_friends_list(self, data):
#         user = self.scope["user"]
#         # Latest Message Subquery
#         latest_message = Message.objects.filter(connection=OuterRef("id")).order_by(
#             "-created"
#         )[:1]
#         # Get Connections for the user
#         connections = (
#             Connection.objects.filter(Q(sender=user) | Q(receiver=user), accepted=True)
#             .annotate(
#                 latest_text=latest_message.values("text"),
#                 latest_created=latest_message.values("created"),
#             )
#             .order_by(Coalesce("latest_created", "updated").desc())
#         )

#         serialized = FriendSerializer(connections, context={"user": user}, many=True)
#         data= {}

#         # send to the requesting user
#         self.send_group(user.username, "friends.list", serialized.data)

#     def receive_request_accept(self, data):
#         username = data.get("username")
#         # Fetch connection object
#         try:
#             connection = Connection.objects.get(
#                 sender__username=username, receiver=self.scope["user"]
#             )
#         except Connection.DoesNotExist:
#             logger.info(f"Error: Connection Not Found")
#             return
#         # Update the connection
#         connection.accepted = True
#         connection.save()

#         serialized = RequestSerializer(connection)

#         # send the accepted requests to both users (receiver and sender)

#         self.send_group(connection.sender.username, "request.accept", serialized.data)
#         self.send_group(connection.receiver.username, "request.accept", serialized.data)

#         # send new serialized friend object to sender

#         serialized_friend = FriendSerializer(
#             connection, context={"user": connection.sender}
#         )
#         self.send_group(
#             connection.sender.username, "friend.new", serialized_friend.data
#         )

#         # send new serialized friend object to receiver

#         serialized_friend = FriendSerializer(
#             connection, context={"user": connection.receiver}
#         )
#         self.send_group(
#             connection.receiver.username, "friend.new", serialized_friend.data
#         )

#     def receive_request_list(self, data):
#         user = self.scope["user"]
#         connections = Connection.objects.filter(receiver=user, accepted=False)

#         serialized = RequestSerializer(connections, many=True)
#         self.send_group(user.username, "request.list", serialized.data)

#     def receive_request_connect(self, data):
#         username = data.get("username")
#         sender = self.scope["user"]
#         try:
#             receiver = User.objects.get(username=username)
#             notification_settings_get_and_check = (
#                 NotificationSettings.objects.get_or_create(user=receiver)
#             )
#             notification_settings = notification_settings_get_and_check[0]
#         except User.DoesNotExist:
#             logger.info(f"username Does Not Exist")
#             return
#         # create connection
#         if connection.sender in receiver.followers.all():
#             connection, _ = Connection.objects.get_or_create(
#                 sender=connection.sender, receiver=receiver, accepted=True
#             )
#         else:
#             connection, _ = Connection.objects.get_or_create(
#                 sender=connection.sender, receiver=receiver
#             )

#         # serialized connection
#         serialized = RequestSerializer(connection)

#         # send it back to sender
#         self.send_group(connection.sender.username, "request.connect", serialized.data)
#         # send to receiver
#         self.send_group(
#             connection.receiver.username, "request.connect", serialized.data
#         )
#         if notification_settings.direct_messages_push_notifications:
#             try:
#             # Save to Notifications model
#                 # notify.send(
#                 #     sender=connection.sender,
#                 #     recipient=receiver,
#                 #     verb="sent a connects request",
#                 #     action_object=connection.sender,
#                 # )
#                 # Send Firebase Notification
#                 send_notification = sendpush(
#                     title="New Connect",
#                     msg=f"{connection.sender} Requests to Connect",
#                     registration_token=receiver.fcm_token,
#                     dataObject={connection.sender},
#                 )
#             except Exception as e:
#                 logger.error(f"Error sending push notification: {e}")

#     def receive_search(self, data):
#         query = data.get("query")
#         user = self.scope["user"]
#         blocked_users = user.blocked_users.all()
#         if query is not None and query != "":
#             users = (
#                 User.objects.filter(
#                     ~Q(id__in=blocked_users),
#                     Q(username__istartswith=query)
#                     | Q(first_name__istartswith=query)
#                     | Q(last_name__istartswith=query),
#                 )
#                 .exclude(username=self.username)
#                 .annotate(
#                     pending_them=Exists(
#                         Connection.objects.filter(
#                             sender=self.scope["user"],
#                             receiver=OuterRef("id"),
#                             accepted=False,
#                         )
#                     ),
#                     pending_me=Exists(
#                         Connection.objects.filter(
#                             sender=OuterRef("id"),
#                             receiver=self.scope["user"],
#                             accepted=False,
#                         )
#                     ),
#                     connected=Exists(
#                         Connection.objects.filter(
#                             Q(sender=self.scope["user"], receiver=OuterRef("id"))
#                             | Q(sender=OuterRef("id"), receiver=self.scope["user"]),
#                             accepted=True,
#                         )
#                     ),
#                 )
#             )

#             # serialied results
#             serialized = SearchSerializer(users, many=True)
#             # logger.info(serialized)
#             # send search results back to this user
#             self.send_group(self.username, "search", serialized.data)
#         else:
#             logger.info("empty query")

#     # =======================================
#     # Catch/ all broadcast to client helpers
#     # =======================================
#     def send_group(self, group, source, data):
#         response = {"type": "broadcast_group", "source": source, "data": data}
#         async_to_sync(self.channel_layer.group_send)(group, response)

#     def broadcast_group(self, data):
#         """
#         data:
#             - type: 'broadcast_group'
#             - source: 'where it originated from'
#             - data: 'what ever you want to send as a dict'
#         """
#         data.pop("type")

#         """
#         data:
#             - source: where it originated from
#             - data: what ever you want to send as a dict
#         """
#         self.send(text_data=json.dumps(data))
