# Generated by Django 4.2.1 on 2023-08-30 10:00

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0007_post_parent"),
    ]

    operations = [
        migrations.RenameModel(
            old_name="Like",
            new_name="PostLike",
        ),
        migrations.AlterUniqueTogether(
            name="postlike",
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name="post",
            name="likes",
            field=models.ManyToManyField(
                blank=True, related_name="post_user", to=settings.AUTH_USER_MODEL
            ),
        ),
    ]
