# Generated by Django 4.2.1 on 2024-04-06 11:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("analytics", "0008_profileviewtrack_viewed_by"),
    ]

    operations = [
        migrations.AddField(
            model_name="accountsperformance",
            name="mentions_count",
            field=models.IntegerField(default=0),
        ),
        migrations.CreateModel(
            name="MentionCount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("count", models.IntegerField(default=0)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mention_count",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
