from rest_framework import serializers
from .models import FollowerCount, AccountsPerformance, ProfileViewTrack, MentionCount


class FollowerCountSerializer(serializers.ModelSerializer):
    class Meta:
        model = FollowerCount
        fields = "__all__"


class AccountsPerformanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountsPerformance
        fields = "__all__"


class ProfileViewsTrackSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProfileViewTrack
        fields = "__all__"


class MentionCountSerializer(serializers.ModelSerializer):
    class Meta:
        model = MentionCount
        fields = "__all__"
