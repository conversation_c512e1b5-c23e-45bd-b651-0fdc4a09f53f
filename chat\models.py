from django.db import models
from core.models import User
from post.models import Post
from cloudinary.models import CloudinaryField


class Connection(models.Model):
    sender = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="sent_connections"
    )
    receiver = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="received_connections"
    )
    accepted = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    blocked = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.sender} sends request to {self.receiver}"


class Message(models.Model):
    connection = models.ForeignKey(
        Connection, on_delete=models.CASCADE, related_name="messages"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="user")
    text = models.TextField()
    # shared posts
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name="shared_post",
        blank=True,
        null=True,
    )
    # Cloudinary fields for image and video uploads
    image = CloudinaryField("messages", blank=True, null=True)
    video = CloudinaryField("video", resource_type="video", blank=True, null=True)
    is_read = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["created"]
        verbose_name_plural = "Message"

    def __str__(self):
        return f"{self.user.username} sent {self.text}"
