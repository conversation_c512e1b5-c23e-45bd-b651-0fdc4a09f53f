from django.db import models
from core.models import User


class TrendingHashtag(models.Model):
    hashtag = models.CharField(max_length=255)
    occurrence = models.IntegerField(default=1)

    def __str__(self) -> str:
        return self.hashtag


class TrendingTopics(models.Model):
    topic = models.CharField(max_length=255)
    occurrence = models.IntegerField(default=1)

    def __str__(self) -> str:
        return self.topic
