from django.db import migrations, models
import django_countries.fields
import django.contrib.postgres.fields
from django.db import connection


class Migration(migrations.Migration):
    dependencies = [
        ("ads", "0004_remove_locationpreferences_state_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="advert",
            name="preferred_location_reach",
        ),
        migrations.DeleteModel(
            name="LocationPreferences",
        ),
        migrations.AddField(
            model_name="advert",
            name="preferred_location_reach",
            field=django_countries.fields.CountryField(
                max_length=2,
                blank=True,
                help_text="Select one or more countries to target this advert.",
            ),
        ),
        # Conditional logic to adjust fields for PostgreSQL or SQLite
        migrations.RunPython(
            code=lambda apps, schema_editor: handle_field_type(apps, schema_editor),
        ),
    ]


def handle_field_type(apps, schema_editor):
    # This logic should be used to handle PostgreSQL and SQLite separately
    from django.db import connection

    Advert = apps.get_model("ads", "Advert")

    if connection.vendor == "postgresql":
        # Add ArrayField if using PostgreSQL
        Advert._meta.get_field(
            "preferred_location_reach"
        ).base_field = django.contrib.postgres.fields.ArrayField(
            base_field=django_countries.fields.CountryField(max_length=2),
            blank=True,
            default=list,
            help_text="Select one or more countries to target this advert.",
            size=None,
        )
    else:
        # Add ManyToManyField if using SQLite
        Advert._meta.get_field(
            "preferred_location_reach"
        ).base_field = models.ManyToManyField(
            "CountryOption",
            related_name="adverts",
            blank=True,
            help_text="Select one or more target countries",
        )
