from django.contrib import admin
from .models import (
    InterestChoice,
    User,
    Interest,
    NotificationSettings,
    Blog,
    Verification,
    PrivacySettings,
)
from unfold.admin import ModelAdmin


@admin.register(User)
class UserAdmin(ModelAdmin):
    list_display = ("id","email", "username", "is_staff", "is_superuser", "is_suspended")
    list_filter = ("is_staff", "is_superuser", "is_suspended")
    search_fields = ("email", "username")
    ordering = ("email",)


@admin.register(Interest)
class InterestAdmin(ModelAdmin):
    list_display = ("user",)
    search_fields = ("user__email", "user__username")
    list_filter = ("user__is_staff",)


class InterestChoiceAdmin(ModelAdmin):
    pass


class NotificationSettingsAdmin(ModelAdmin):
    list_display = ["user"]
    search_fields = ["user__email", "user__username"]


class BlogAdmin(ModelAdmin):
    list_display = ["name", "link", "location", "interest"]
    search_fields = ["name"]
    list_filter = ["location", "interest"]


class VerificationAdmin(ModelAdmin):
    list_display = ["account_username", "account_type", "email", "created_at"]
    list_filter = ["account_type", "category", "created_at"]
    search_fields = ["account_username", "email", "legal_name", "business_name", "govt_entity_name"]




class PrivacyAdmin(ModelAdmin):
    list_display = ["user", "comments", "tags",]
    search_fields = ["user"]
    list_filter = ["user", "tags"]

admin.site.register(InterestChoice, InterestChoiceAdmin)
admin.site.register(NotificationSettings, NotificationSettingsAdmin)
admin.site.register(Blog, BlogAdmin)
admin.site.register(Verification, VerificationAdmin)
admin.site.register(PrivacySettings, PrivacyAdmin)




INDEX_TITLE = "SmallWorld Admin Dashboard"
admin.site.index_title = INDEX_TITLE