from rest_framework import serializers
from .models import (
    User,
    Interest,
    InterestChoice,
    Verification,
    NotificationSettings,
    PrivacySettings,
    Blog,
)
import pyotp
from django_countries.serializers import CountryFieldMixin
from phonenumber_field.serializerfields import PhoneNumberField
from rest_framework_simplejwt.serializers import (
    TokenObtainPairSerializer as JwtTokenObtainPairSerializer,
)
from drf_writable_nested.serializers import WritableNestedModelSerializer

from django.contrib.auth import get_user_model
from django.db import IntegrityError
from notifications.models import Notification


class TokenObtainPairSerializer(JwtTokenObtainPairSerializer):
    username_field = get_user_model().USERNAME_FIELD


class MailSerializer(serializers.Serializer):
    """
    MailSerializer
    """

    email = serializers.EmailField()

    class Meta:
        fields = ["email"]


class BlogSerializer(serializers.ModelSerializer):
    class Meta:
        model = Blog
        fields = "__all__"



class UserSerializer(CountryFieldMixin, serializers.ModelSerializer):
    """
    The User Registration Serializers
    """

    phone_number = PhoneNumberField()
    profile_picture = serializers.SerializerMethodField()
    cover_photo = serializers.SerializerMethodField()

    def render_url(self, cloudinary_picture_object, obj):
        if isinstance(obj, str):
            return obj
        elif cloudinary_picture_object:
            if cloudinary_picture_object.url[:5] == "https":
                return cloudinary_picture_object.url
            else:
                return cloudinary_picture_object.url.replace("http", "https")
        return None

    def get_cover_photo(self, obj):
        return self.render_url(obj.cover_photo, obj)

    def get_profile_picture(self, obj):
        return self.render_url(obj.profile_picture, obj)

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "first_name",
            "last_name",
            "profile_picture",
            "cover_photo",
            "bio",
            "phone_number",
            "birthday",
            "country_of_residence",
            "email",
            "is_suspended",
            "date_joined",
            "verification_status",
            "password",
            "followers",
            "following",
            "counts",
            "mfa_hash",

        ]
        read_only_fields = ["id", "followers", "following", "counts", "mfa_hash", "date_joined"]

        extra_kwargs = {
            "password": {"write_only": True},
        }
        ref_name = "CoreUser"

    def create(self, validated_data):
        # clean all data, set as lowercase
        username = validated_data["username"]
        email = validated_data["email"].lower()
        password = validated_data["password"]
        first_name = validated_data["first_name"]
        last_name = validated_data["last_name"]
        birthday = validated_data["birthday"]
        country_of_residence = validated_data["country_of_residence"].lower()
        phone_number = validated_data["phone_number"]

        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            birthday=birthday,
            country_of_residence=country_of_residence,
            phone_number=phone_number,
        )
        user.mfa_hash = pyotp.random_base32()
        user.save()
        return user


class EmailVerificationSerializer(serializers.ModelSerializer):
    token = serializers.CharField(max_length=555)

    class Meta:
        model = User
        fields = [
            "token",
        ]


class ResetPasswordSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=5)
    password = serializers.CharField()
    confirm_password = serializers.CharField()

    class Meta:
        fields = ("code", "password", "confirm_password")


class VerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Verification
        fields = [
            "account_type",
            "account_username",
            "email",
            "category",
            "date_of_birth",
            "legal_name",
            "government_id",
            "proof_of_address",
            "recent_photo",
            "reason_for_verification",
            "additional_info",
            # Business fields
            "business_name",
            "business_doc",
            "business_address_proof",
            "business_premises_photo",
            # Government fields
            "govt_entity_name",
            "govt_authorized_id",
            "govt_account_verification_letter",
            "govt_registration_docs",
            # Official fields
            "govt_agency",
            "official_id_card",
            "official_letter",
        ]


class InterestChoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = InterestChoice
        fields = ["id", "name", "thumbnail"]
        extra_kwargs = {"name": {"required": True}}


class InterestSerializer(WritableNestedModelSerializer):
    # Accepts a list of InterestChoice IDs for input, but returns full objects on output
    choices = serializers.PrimaryKeyRelatedField(
        queryset=InterestChoice.objects.all(), many=True, write_only=True, required=True
    )
    choices_details = InterestChoiceSerializer(
        source="choices", many=True, read_only=True
    )

    class Meta:
        model = Interest
        fields = ["user", "choices", "choices_details"]

    def validate_choices(self, value):
        if len(value) > 5:
            raise serializers.ValidationError(
                "You can select a maximum of 5 interests."
            )
        return value

    def to_representation(self, instance):
        # Only return the user and the detailed choices (not the input IDs)
        ret = super().to_representation(instance)
        ret.pop("choices", None)  # Remove the write-only field from output
        return ret


class NotificationSettingsSerializers(serializers.ModelSerializer):
    class Meta:
        model = NotificationSettings
        fields = [
            "user",
            "enable_comment_push_notifications",
            "direct_messages_push_notifications",
            "likes_push_notifications",
            "enable_boost_push_notifications",
            "tags_to_post_push_notifications",
            "connections_push_notifications",
            "blogs_post_push_notifications",
            "direct_messages_email_notifications",
            "promotional_email_from_smallworld",
            "two_factor_authentication_email",
            "two_factor_authentication_google_auth",
        ]


class PrivacySettingsSerializers(serializers.ModelSerializer):
    class Meta:
        model = PrivacySettings
        fields = "__all__"


class InappNotificationSerializer(serializers.ModelSerializer):
    """
    Serializer for notifications. Includes fields for recipient, actor, target, action_object,
    timestamp, unread status, and notification level. Handles serialization of notification
    objects with related user data.
    """

    actor = UserSerializer()
    recipient = UserSerializer()
    target = UserSerializer()
    action_object = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = [
            "id",
            "recipient",
            "actor",
            "verb",
            "description",
            "target",
            "action_object",
            "timestamp",
            "unread",
            "level",
        ]
        read_only_fields = fields

    def get_action_object(self, obj):
        return str(obj.action_object) if obj.action_object else None
