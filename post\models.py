from django.db import models
from core.models import User
from cloudinary.models import CloudinaryField
from django.db.models import Q, Count, QuerySet
from ads.utils import relevant_ads_filter
from rest_framework.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime
from django.contrib.contenttypes.fields import Generic<PERSON><PERSON><PERSON><PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType



class Boost(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="user_boosts")
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    
    content_object = GenericForeignKey('content_type', 'object_id')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'content_type', 'object_id')  # Prevent multiple boosts of same object by same user
    
class PostLike(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="postlike")
    post = models.ForeignKey("Post", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} liked {self.post.id} post at {self.created_at}"


class CommentLike(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="commentlike")
    comment = models.ForeignKey("Comment", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} liked {self.comment.id} comment at {self.created_at}"



"""
============================================ POST ============================================
"""
class PostQuerySet(QuerySet):
    def by_username(self, username):
        return self.filter(user__username__iexact=username)

    def feed(self, user):
        following_exists = user.following.exists()

        # Collect necessary related lists
        blocked_users = user.blocked_users.all()
        muted_users = user.muted_users.all()
        reported_posts = user.reported_post.values_list("post__id", flat=True)
        # Collect followed users' IDs
        followed_users_id = []
        if following_exists:
            followed_users_id = list(
                user.following.values_list("post_user__id", flat=True)
            )

        # Filter regular posts
        regular_posts = (
            self.filter(
                ~Q(user__id__in=muted_users),
                ~Q(user__id__in=blocked_users),
                ~Q(id__in=reported_posts),
                Q(user__id__in=followed_users_id) | Q(user=user),
            )
            .prefetch_related("likes", "images", "comments")
            .distinct()
        )

        # Get recommended posts
        recommended_posts = Post.objects.annotate(like_count=Count("likes")).order_by(
            "-like_count"
        )[:10]

        if not recommended_posts.exists():
            recommended_posts = Post.objects.order_by("-post_date")[:10]

        if not regular_posts:
            regular_posts = recommended_posts
            # print("-------------------")

        # Fetch relevant ads
        try:
            relevant_ads = list(relevant_ads_filter(user))
        except AttributeError:
            raise ValidationError("User does not have any interests")

        # Combine posts and ads
        combined_feed_content = []
        post_index, ad_index = 0, 0

        while post_index < len(regular_posts) or ad_index < len(relevant_ads):
            if post_index % 4 == 0 and ad_index < len(relevant_ads):
                combined_feed_content.append(relevant_ads[ad_index])
                ad_index += 1
            if post_index < len(regular_posts):
                combined_feed_content.append(regular_posts[post_index])
                post_index += 1

        # Sort combined feed by post date
        combined_feed_content.sort(
            key=lambda x: x.post_date if isinstance(x, Post) else x["post_date"],
            reverse=True,
        )

        # Serialize the combined content
        from .serializers import PostSerializer

        serialized_content = []
        for item in combined_feed_content:
            if isinstance(item, Post):
                serializer = PostSerializer(item)
                serialized_content.append(serializer.data)
            else:
                serialized_content.append(
                    item
                )  # Assuming ads are already serialized or are dictionaries

        return serialized_content


class PostManager(models.Manager):
    def get_queryset(self):
        return PostQuerySet(self.model, using=self._db)

    def feed(self, user):
        return self.get_queryset().feed(user)


class Post(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="post")
    content = models.TextField(blank=True, null=True)
    parent = models.ForeignKey(
        "self", on_delete=models.SET_NULL, related_name="replies", blank=True, null=True
    )
    post_date = models.DateTimeField(auto_now_add=True)

    likes = models.ManyToManyField(
        User, related_name="post_user", blank=True, through=PostLike
    )
    boosts = GenericRelation('Boost', related_query_name='post')

    is_published = models.BooleanField(default=False)

    scheduled_date = models.DateField(null=True, blank=True)  # Scheduled date
    scheduled_time = models.TimeField(null=True, blank=True)  # Scheduled time

    objects = PostManager()

    class Meta:
        ordering = [
            "-post_date",
        ]

    @property
    def items_count(self):
        from post.models import Boost  # Avoid circular import issues

        post_ct = ContentType.objects.get_for_model(Post)

        boost_count = Boost.objects.filter(
            content_type=post_ct,
            object_id=self.id
        ).count()
        like_count = self.likes.count()
        comment_count = self.comments.count()
        counts = {
            "boost_count": boost_count,
            "like_count": like_count,
            "comment_count": comment_count,
        }
        return counts

    @property
    def post_thread(self):
        thread_posts = Post.objects.prefetch_related().filter(
            Q(parent=self), ~Q(content=None), Q(user=self.user)
        )
        return thread_posts

    def to_dict(self):
        return_dict = {
            "id": self.id,
            "user": self.user,
            "content": self.content,
            "parent": self.parent if self.parent else None,
            "post_date": self.post_date.isoformat(),
            "items_count": self.items_count,
            "post_thread": [post.to_dict() for post in self.post_thread],
            "media": [image for image in self.images.all() if image],
        }

        return return_dict

    def save(self, *args, **kwargs):
        # Automatically publish if scheduled time has passed
        if self.scheduled_date and self.scheduled_time:
            scheduled_datetime = timezone.make_aware(
                datetime.combine(self.scheduled_date, self.scheduled_time)
            )
            if scheduled_datetime <= timezone.now():
                self.is_published = True
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} ====== {self.id}"

class PostImage(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name="images")
    media = CloudinaryField("media", blank=True, null=True, resource_type="auto")


    def __str__(self):
        return f"{self.post.user.username} - ({self.id})"

"""
============================= COMMENT ===================================================

"""


class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name="comments")
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="commented_by"
    )
    content = models.TextField()
    boosts = GenericRelation(Boost, related_query_name='comment')
    parent = models.ForeignKey(
        "self", on_delete=models.SET_NULL, blank=True, null=True, related_name="thread"
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    likes = models.ManyToManyField(
        User, related_name="comment_like", blank=True, through=CommentLike
    )

    class Meta:
        ordering = [
            "-timestamp",
        ]

    # @property
    # def is_boost(self):
    #     return self.parent != None and self.content == None

    @property
    def comment_thread(self):
        thread_comment = Post.objects.prefetch_related().filter(parent=self)
        return thread_comment

    def __str__(self) -> str:
        return f"{self.user} commented on {self.post} ---------> commentid ({self.id})"


class Mention(models.Model):
    """
    The Mentions Model
    """

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="mentions")
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="mentions",
    )
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="mentions",
    )

    start_index = models.IntegerField(null=True, blank=True)
    end_index = models.IntegerField(null=True, blank=True)

    is_read = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return f"{self.user} mentions you"


class Collection(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="owner")
    name = models.CharField(max_length=256)

    def __str__(self) -> str:
        return f'{self.user}"s collection'


class SavedPost(models.Model):
    """
    The Saved Post Model
    """

    collection = models.ForeignKey(
        Collection, on_delete=models.CASCADE, related_name="saved_posts"
    )
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name="saved_posts")

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["collection", "post"], name="unique_saved_post"
            )
        ]

    def __str__(self) -> str:
        return self.post.__str__()


class ReportPost(models.Model):
    REASON_CHOICES = (
        ("misinformation", "Misinformation/Fake News"),
        ("discrimination", "Hate Speech and Discrimination"),
        ("violence-threat", "Threats of Violence Against Others"),
        ("self-harm-thread", "Threats of Self-Harm"),
        ("harassment", "Harrassment & Bullying"),
        ("nudity", "Nudity & Sexual Content"),
        ("spam", "Spam & Irrelivant Content"),
    )
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="reported_post"
    )
    reason = models.CharField(max_length=50, choices=REASON_CHOICES, blank=True)
    post = models.ForeignKey(
        Post, on_delete=models.CASCADE, related_name="reported_post"
    )
    additional_comment = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        name = self.post
        return str(name)


class UninterestedPost(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="uninterested_posts"
    )
    post = models.ForeignKey(
        Post, on_delete=models.CASCADE, related_name="uninterested_posts"
    )

    def __str__(self):
        name = self.post
        return str(name)
