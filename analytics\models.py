from django.db import models
from core.models import User
from django.core.exceptions import ValidationError


# class FollowerCount(models.Model):
#     user = models.ForeignKey(
#         User, on_delete=models.CASCADE, related_name="follower_count"
#     )
#     count = models.IntegerField(default=0)
#     timestamp = models.DateTimeField(auto_now_add=True)

#     def __str__(self):
#         return f"{self.user}: {self.count} at {self.timestamp}"


class FollowerCount(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="follower_count", null=True
    )
    follower = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="following_count", null=True
    )
    count = models.IntegerField(default=1)  # Always 1 per follow
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = (
            "user",
            "follower",
        )  # Ensures one user cannot follow the same person twice

    def clean(self):
        """Override clean method to check if follower exists."""
        if FollowerCount.objects.filter(
            user=self.user, follower=self.follower
        ).exists():
            raise ValidationError("This follower relationship already exists.")
        # Ensure count is always 1
        if self.count != 1:
            raise ValidationError("Follower count should always be 1.")

    def save(self, *args, **kwargs):
        self.clean()  # Perform custom validation before saving
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.follower} → {self.user} at {self.timestamp}"


class MentionCount(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="mention_count"
    )
    count = models.IntegerField(default=0)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user}: {self.count} at {self.timestamp}"


class AccountsPerformance(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="accounts_performance"
    )
    like_count = models.IntegerField(default=0)
    comment_count = models.IntegerField(default=0)
    boost_count = models.IntegerField(default=0)
    mentions_count = models.IntegerField(default=0)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user}: {self.like_count} likes, {self.comment_count} comments, {self.boost_count} boosts, {self.mentions_count} mentions at {self.timestamp}"


class ProfileViewTrack(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="profile_views_count"
    )
    viewed_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="profile_viewed_by"
    )
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user}: {self.viewed_by} at {self.timestamp}"
